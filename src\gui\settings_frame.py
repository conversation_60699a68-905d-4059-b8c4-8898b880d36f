"""
Settings frame for TikTok Automation
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Optional, List, Callable
import logging
import json

from src.utils.config import save_config, update_config

class SettingsFrame(ttk.Frame):
    """Frame for managing application settings"""

    def __init__(self, parent, config: Dict[str, Any], status_callback: Callable[[str], None]):
        """Initialize settings frame

        Args:
            parent: Parent widget
            config: Application configuration
            status_callback: Callback function for status updates
        """
        super().__init__(parent)
        self.config = config
        self.set_status = status_callback
        self.config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                       "config.json")

        # Create UI elements
        self.create_widgets()

        # Load settings
        self.load_settings()

    def create_widgets(self):
        """Create UI widgets"""
        # Create notebook for settings categories
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create settings tabs
        self.create_ai_settings()
        self.create_tts_settings()
        self.create_image_settings()
        self.create_video_settings()
        self.create_export_settings()
        self.create_api_settings()

        # Create action buttons
        self.create_action_buttons()

    def create_ai_settings(self):
        """Create AI settings tab"""
        ai_frame = ttk.Frame(self.notebook)
        self.notebook.add(ai_frame, text="AI Settings")

        # Create settings form
        form_frame = ttk.LabelFrame(ai_frame, text="Story Generation Settings")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Provider
        ttk.Label(form_frame, text="Provider:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.ai_provider_var = tk.StringVar()
        provider_combo = ttk.Combobox(form_frame, textvariable=self.ai_provider_var, width=30)
        provider_combo['values'] = ("openai", "huggingface", "local")
        provider_combo.grid(row=0, column=1, padx=10, pady=5)

        # Model
        ttk.Label(form_frame, text="Model:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        self.ai_model_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.ai_model_var, width=32).grid(row=1, column=1, padx=10, pady=5)

        # Temperature
        ttk.Label(form_frame, text="Temperature:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        self.ai_temperature_var = tk.DoubleVar()
        ttk.Spinbox(form_frame, from_=0.1, to=1.0, increment=0.1, textvariable=self.ai_temperature_var, width=31).grid(row=2, column=1, padx=10, pady=5)

        # Max Tokens
        ttk.Label(form_frame, text="Max Tokens:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        self.ai_max_tokens_var = tk.IntVar()
        ttk.Spinbox(form_frame, from_=100, to=4000, increment=100, textvariable=self.ai_max_tokens_var, width=31).grid(row=3, column=1, padx=10, pady=5)

    def create_tts_settings(self):
        """Create TTS settings tab"""
        tts_frame = ttk.Frame(self.notebook)
        self.notebook.add(tts_frame, text="TTS Settings")

        # Create settings form
        form_frame = ttk.LabelFrame(tts_frame, text="Text-to-Speech Settings")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Provider
        ttk.Label(form_frame, text="Provider:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.tts_provider_var = tk.StringVar()
        provider_combo = ttk.Combobox(form_frame, textvariable=self.tts_provider_var, width=30)
        provider_combo['values'] = ("tts", "bark")
        provider_combo.grid(row=0, column=1, padx=10, pady=5)

        # Voice Options
        voice_frame = ttk.LabelFrame(form_frame, text="Voice Options")
        voice_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=10, pady=10)

        # Male Voices
        ttk.Label(voice_frame, text="Male Voices:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.male_voices_var = tk.StringVar()
        ttk.Entry(voice_frame, textvariable=self.male_voices_var, width=32).grid(row=0, column=1, padx=10, pady=5)
        ttk.Label(voice_frame, text="(comma-separated)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # Female Voices
        ttk.Label(voice_frame, text="Female Voices:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        self.female_voices_var = tk.StringVar()
        ttk.Entry(voice_frame, textvariable=self.female_voices_var, width=32).grid(row=1, column=1, padx=10, pady=5)
        ttk.Label(voice_frame, text="(comma-separated)").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # AI Voices
        ttk.Label(voice_frame, text="AI Voices:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        self.ai_voices_var = tk.StringVar()
        ttk.Entry(voice_frame, textvariable=self.ai_voices_var, width=32).grid(row=2, column=1, padx=10, pady=5)
        ttk.Label(voice_frame, text="(comma-separated)").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)

    def create_image_settings(self):
        """Create image settings tab"""
        image_frame = ttk.Frame(self.notebook)
        self.notebook.add(image_frame, text="Image Settings")

        # Create settings form
        form_frame = ttk.LabelFrame(image_frame, text="Image Generation Settings")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Provider
        ttk.Label(form_frame, text="Provider:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.image_provider_var = tk.StringVar()
        provider_combo = ttk.Combobox(form_frame, textvariable=self.image_provider_var, width=30)
        provider_combo['values'] = ("stable_diffusion", "craiyon")
        provider_combo.grid(row=0, column=1, padx=10, pady=5)

        # Resolution
        resolution_frame = ttk.LabelFrame(form_frame, text="Resolution")
        resolution_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=10, pady=10)

        # Width
        ttk.Label(resolution_frame, text="Width:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.image_width_var = tk.IntVar()
        ttk.Spinbox(resolution_frame, from_=480, to=1920, increment=8, textvariable=self.image_width_var, width=10).grid(row=0, column=1, padx=10, pady=5)

        # Height
        ttk.Label(resolution_frame, text="Height:").grid(row=0, column=2, sticky=tk.W, padx=10, pady=5)
        self.image_height_var = tk.IntVar()
        ttk.Spinbox(resolution_frame, from_=480, to=1920, increment=8, textvariable=self.image_height_var, width=10).grid(row=0, column=3, padx=10, pady=5)

    def create_video_settings(self):
        """Create video settings tab"""
        video_frame = ttk.Frame(self.notebook)
        self.notebook.add(video_frame, text="Video Settings")

        # Create settings form
        form_frame = ttk.LabelFrame(video_frame, text="Video Generation Settings")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Format
        ttk.Label(form_frame, text="Format:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.video_format_var = tk.StringVar()
        format_combo = ttk.Combobox(form_frame, textvariable=self.video_format_var, width=10)
        format_combo['values'] = ("mp4", "mov", "avi")
        format_combo.grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)

        # FPS
        ttk.Label(form_frame, text="FPS:").grid(row=0, column=2, sticky=tk.W, padx=10, pady=5)
        self.video_fps_var = tk.IntVar()
        ttk.Spinbox(form_frame, from_=24, to=60, increment=1, textvariable=self.video_fps_var, width=10).grid(row=0, column=3, sticky=tk.W, padx=10, pady=5)

        # Resolution
        resolution_frame = ttk.LabelFrame(form_frame, text="Resolution")
        resolution_frame.grid(row=1, column=0, columnspan=4, sticky=tk.EW, padx=10, pady=10)

        # Width
        ttk.Label(resolution_frame, text="Width:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.video_width_var = tk.IntVar()
        ttk.Spinbox(resolution_frame, from_=480, to=1920, increment=8, textvariable=self.video_width_var, width=10).grid(row=0, column=1, padx=10, pady=5)

        # Height
        ttk.Label(resolution_frame, text="Height:").grid(row=0, column=2, sticky=tk.W, padx=10, pady=5)
        self.video_height_var = tk.IntVar()
        ttk.Spinbox(resolution_frame, from_=480, to=1920, increment=8, textvariable=self.video_height_var, width=10).grid(row=0, column=3, padx=10, pady=5)

        # Effects
        effects_frame = ttk.LabelFrame(form_frame, text="Effects")
        effects_frame.grid(row=2, column=0, columnspan=4, sticky=tk.EW, padx=10, pady=10)

        # Enable Effects
        self.effects_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(effects_frame, text="Enable Effects", variable=self.effects_enabled_var).grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)

        # Default Effect
        ttk.Label(effects_frame, text="Default Effect:").grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        self.default_effect_var = tk.StringVar()
        effect_combo = ttk.Combobox(effects_frame, textvariable=self.default_effect_var, width=15)
        effect_combo['values'] = ("zoom", "pan", "fade", "pulse")
        effect_combo.grid(row=0, column=2, padx=10, pady=5)

        # Subtitles
        subtitles_frame = ttk.LabelFrame(form_frame, text="Subtitles")
        subtitles_frame.grid(row=3, column=0, columnspan=4, sticky=tk.EW, padx=10, pady=10)

        # Enable Subtitles
        self.subtitles_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(subtitles_frame, text="Enable Subtitles", variable=self.subtitles_enabled_var).grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)

        # Font
        ttk.Label(subtitles_frame, text="Font:").grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        self.subtitle_font_var = tk.StringVar()
        ttk.Entry(subtitles_frame, textvariable=self.subtitle_font_var, width=15).grid(row=0, column=2, padx=10, pady=5)

        # Font Size
        ttk.Label(subtitles_frame, text="Size:").grid(row=0, column=3, sticky=tk.W, padx=10, pady=5)
        self.subtitle_size_var = tk.IntVar()
        ttk.Spinbox(subtitles_frame, from_=20, to=80, increment=2, textvariable=self.subtitle_size_var, width=10).grid(row=0, column=4, padx=10, pady=5)

        # Colors
        ttk.Label(subtitles_frame, text="Color:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        self.subtitle_color_var = tk.StringVar()
        ttk.Entry(subtitles_frame, textvariable=self.subtitle_color_var, width=15).grid(row=1, column=1, padx=10, pady=5)

        ttk.Label(subtitles_frame, text="Stroke:").grid(row=1, column=2, sticky=tk.W, padx=10, pady=5)
        self.subtitle_stroke_color_var = tk.StringVar()
        ttk.Entry(subtitles_frame, textvariable=self.subtitle_stroke_color_var, width=15).grid(row=1, column=3, padx=10, pady=5)

        ttk.Label(subtitles_frame, text="Width:").grid(row=1, column=4, sticky=tk.W, padx=10, pady=5)
        self.subtitle_stroke_width_var = tk.IntVar()
        ttk.Spinbox(subtitles_frame, from_=0, to=5, increment=1, textvariable=self.subtitle_stroke_width_var, width=5).grid(row=1, column=5, padx=10, pady=5)

    def create_export_settings(self):
        """Create export settings tab"""
        export_frame = ttk.Frame(self.notebook)
        self.notebook.add(export_frame, text="Export Settings")

        # Create settings form
        form_frame = ttk.LabelFrame(export_frame, text="Export Settings")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Output Directory
        ttk.Label(form_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)

        dir_frame = ttk.Frame(form_frame)
        dir_frame.grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        self.output_dir_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=30).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(dir_frame, text="Browse", command=self.browse_output_dir).pack(side=tk.RIGHT, padx=5)

        # Metadata
        metadata_frame = ttk.LabelFrame(form_frame, text="Metadata")
        metadata_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=10, pady=10)

        # Enable Metadata
        self.metadata_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(metadata_frame, text="Export Metadata", variable=self.metadata_enabled_var).grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)

        # Format
        ttk.Label(metadata_frame, text="Format:").grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        self.metadata_format_var = tk.StringVar()
        format_combo = ttk.Combobox(metadata_frame, textvariable=self.metadata_format_var, width=10)
        format_combo['values'] = ("json",)
        format_combo.grid(row=0, column=2, padx=10, pady=5)

    def create_api_settings(self):
        """Create API settings tab"""
        api_frame = ttk.Frame(self.notebook)
        self.notebook.add(api_frame, text="API Keys")

        # Create settings form
        form_frame = ttk.LabelFrame(api_frame, text="API Keys")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # OpenAI API Key
        ttk.Label(form_frame, text="OpenAI API Key:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        self.openai_key_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.openai_key_var, width=40, show="*").grid(row=0, column=1, padx=10, pady=5)

        # Replicate API Key (for Image Generation)
        ttk.Label(form_frame, text="Replicate API Key (Optional):").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        self.replicate_key_var = tk.StringVar()
        replicate_entry = ttk.Entry(form_frame, textvariable=self.replicate_key_var, width=40, show="*")
        replicate_entry.grid(row=1, column=1, padx=10, pady=5)

        # Add info button for Replicate
        ttk.Button(form_frame, text="?", width=3,
                  command=lambda: self.show_api_info("replicate")).grid(row=1, column=2, padx=5)

        # Stability AI API Key (Alternative for Image Generation)
        ttk.Label(form_frame, text="Stability AI API Key (Optional):").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        self.stability_key_var = tk.StringVar()
        stability_entry = ttk.Entry(form_frame, textvariable=self.stability_key_var, width=40, show="*")
        stability_entry.grid(row=2, column=1, padx=10, pady=5)

        # Add info button for Stability AI
        ttk.Button(form_frame, text="?", width=3,
                  command=lambda: self.show_api_info("stability")).grid(row=2, column=2, padx=5)

        # ElevenLabs API Key
        ttk.Label(form_frame, text="ElevenLabs API Key:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        self.elevenlabs_key_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.elevenlabs_key_var, width=40, show="*").grid(row=3, column=1, padx=10, pady=5)

        # Hedra API Key
        ttk.Label(form_frame, text="Hedra API Key:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        self.hedra_key_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.hedra_key_var, width=40, show="*").grid(row=4, column=1, padx=10, pady=5)

        # ImgBB API Key
        ttk.Label(form_frame, text="ImgBB API Key:").grid(row=5, column=0, sticky=tk.W, padx=10, pady=5)
        self.imgbb_key_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.imgbb_key_var, width=40, show="*").grid(row=5, column=1, padx=10, pady=5)

        # Note about API keys
        note_frame = ttk.LabelFrame(api_frame, text="Note")
        note_frame.pack(fill=tk.X, padx=10, pady=10)

        note_text = "API keys are stored in your configuration file. Keep this file secure."
        ttk.Label(note_frame, text=note_text, wraplength=400).pack(padx=10, pady=10)

    def create_action_buttons(self):
        """Create action buttons"""
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # Save button
        ttk.Button(button_frame, text="Save Settings", command=self.save_settings).pack(side=tk.LEFT, padx=5)

        # Reset button
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_settings).pack(side=tk.LEFT, padx=5)

    def load_settings(self):
        """Load settings from config"""
        # AI settings
        self.ai_provider_var.set(self.config['ai']['story_generation']['provider'])
        self.ai_model_var.set(self.config['ai']['story_generation']['model'])
        self.ai_temperature_var.set(self.config['ai']['story_generation']['temperature'])
        self.ai_max_tokens_var.set(self.config['ai']['story_generation']['max_tokens'])

        # TTS settings
        self.tts_provider_var.set(self.config['tts']['provider'])
        self.male_voices_var.set(', '.join(self.config['tts']['voice_options']['male']))
        self.female_voices_var.set(', '.join(self.config['tts']['voice_options']['female']))
        self.ai_voices_var.set(', '.join(self.config['tts']['voice_options']['ai']))

        # Image settings
        self.image_provider_var.set(self.config['image']['provider'])
        self.image_width_var.set(self.config['image']['resolution']['width'])
        self.image_height_var.set(self.config['image']['resolution']['height'])

        # Video settings
        self.video_format_var.set(self.config['video']['format'])
        self.video_fps_var.set(self.config['video']['fps'])
        self.video_width_var.set(self.config['video']['resolution']['width'])
        self.video_height_var.set(self.config['video']['resolution']['height'])
        self.effects_enabled_var.set(self.config['video']['effects']['enabled'])
        self.default_effect_var.set(self.config['video']['effects']['default'])
        self.subtitles_enabled_var.set(self.config['video']['subtitles']['enabled'])
        self.subtitle_font_var.set(self.config['video']['subtitles']['font'])
        self.subtitle_size_var.set(self.config['video']['subtitles']['size'])
        self.subtitle_color_var.set(self.config['video']['subtitles']['color'])
        self.subtitle_stroke_color_var.set(self.config['video']['subtitles']['stroke_color'])
        self.subtitle_stroke_width_var.set(self.config['video']['subtitles']['stroke_width'])

        # Export settings
        self.output_dir_var.set(self.config['export']['output_dir'])
        self.metadata_enabled_var.set(self.config['export']['metadata']['enabled'])
        self.metadata_format_var.set(self.config['export']['metadata']['format'])

        # API keys
        self.openai_key_var.set(self.config['api_keys'].get('openai', ''))
        self.replicate_key_var.set(self.config['api_keys'].get('replicate', ''))
        self.stability_key_var.set(self.config['api_keys'].get('stability', ''))
        self.elevenlabs_key_var.set(self.config['api_keys'].get('elevenlabs', ''))
        self.hedra_key_var.set(self.config['api_keys'].get('hedra', ''))
        self.imgbb_key_var.set(self.config['api_keys'].get('imgbb', ''))

    def save_settings(self):
        """Save settings to config file"""
        try:
            # Update config with current values

            # AI settings
            self.config['ai']['story_generation']['provider'] = self.ai_provider_var.get()
            self.config['ai']['story_generation']['model'] = self.ai_model_var.get()
            self.config['ai']['story_generation']['temperature'] = self.ai_temperature_var.get()
            self.config['ai']['story_generation']['max_tokens'] = self.ai_max_tokens_var.get()

            # TTS settings
            self.config['tts']['provider'] = self.tts_provider_var.get()
            self.config['tts']['voice_options']['male'] = [v.strip() for v in self.male_voices_var.get().split(',') if v.strip()]
            self.config['tts']['voice_options']['female'] = [v.strip() for v in self.female_voices_var.get().split(',') if v.strip()]
            self.config['tts']['voice_options']['ai'] = [v.strip() for v in self.ai_voices_var.get().split(',') if v.strip()]

            # Image settings
            self.config['image']['provider'] = self.image_provider_var.get()
            self.config['image']['resolution']['width'] = self.image_width_var.get()
            self.config['image']['resolution']['height'] = self.image_height_var.get()

            # Video settings
            self.config['video']['format'] = self.video_format_var.get()
            self.config['video']['fps'] = self.video_fps_var.get()
            self.config['video']['resolution']['width'] = self.video_width_var.get()
            self.config['video']['resolution']['height'] = self.video_height_var.get()
            self.config['video']['effects']['enabled'] = self.effects_enabled_var.get()
            self.config['video']['effects']['default'] = self.default_effect_var.get()
            self.config['video']['subtitles']['enabled'] = self.subtitles_enabled_var.get()
            self.config['video']['subtitles']['font'] = self.subtitle_font_var.get()
            self.config['video']['subtitles']['size'] = self.subtitle_size_var.get()
            self.config['video']['subtitles']['color'] = self.subtitle_color_var.get()
            self.config['video']['subtitles']['stroke_color'] = self.subtitle_stroke_color_var.get()
            self.config['video']['subtitles']['stroke_width'] = self.subtitle_stroke_width_var.get()

            # Export settings
            self.config['export']['output_dir'] = self.output_dir_var.get()
            self.config['export']['metadata']['enabled'] = self.metadata_enabled_var.get()
            self.config['export']['metadata']['format'] = self.metadata_format_var.get()

            # API keys
            self.config['api_keys']['openai'] = self.openai_key_var.get()
            self.config['api_keys']['replicate'] = self.replicate_key_var.get()
            self.config['api_keys']['stability'] = self.stability_key_var.get()
            self.config['api_keys']['elevenlabs'] = self.elevenlabs_key_var.get()
            self.config['api_keys']['hedra'] = self.hedra_key_var.get()
            self.config['api_keys']['imgbb'] = self.imgbb_key_var.get()

            # Save to file
            save_config(self.config_path, self.config)

            self.set_status("Settings saved successfully")
            messagebox.showinfo("Success", "Settings saved successfully")
        except Exception as e:
            error_msg = f"Error saving settings: {str(e)}"
            self.set_status(error_msg)
            messagebox.showerror("Error", error_msg)

    def reset_settings(self):
        """Reset settings to defaults"""
        if not messagebox.askyesno("Confirm Reset",
                                  "Are you sure you want to reset all settings to defaults?"):
            return

        try:
            # Load default config
            from src.utils.config import DEFAULT_CONFIG

            # Update config
            self.config = DEFAULT_CONFIG.copy()

            # Save API keys (don't reset these)
            openai_key = self.openai_key_var.get()
            replicate_key = self.replicate_key_var.get()
            stability_key = self.stability_key_var.get()
            elevenlabs_key = self.elevenlabs_key_var.get()
            hedra_key = self.hedra_key_var.get()
            imgbb_key = self.imgbb_key_var.get()

            if openai_key:
                self.config['api_keys']['openai'] = openai_key
            if replicate_key:
                self.config['api_keys']['replicate'] = replicate_key
            if stability_key:
                self.config['api_keys']['stability'] = stability_key
            if elevenlabs_key:
                self.config['api_keys']['elevenlabs'] = elevenlabs_key
            if hedra_key:
                self.config['api_keys']['hedra'] = hedra_key
            if imgbb_key:
                self.config['api_keys']['imgbb'] = imgbb_key

            # Reload settings
            self.load_settings()

            self.set_status("Settings reset to defaults")
            messagebox.showinfo("Success", "Settings reset to defaults")
        except Exception as e:
            error_msg = f"Error resetting settings: {str(e)}"
            self.set_status(error_msg)
            messagebox.showerror("Error", error_msg)

    def browse_output_dir(self):
        """Browse for output directory"""
        current_dir = self.output_dir_var.get()
        if not os.path.isdir(current_dir):
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        directory = filedialog.askdirectory(initialdir=current_dir)
        if directory:
            self.output_dir_var.set(directory)

    def show_api_info(self, api_type: str):
        """Show information about specific API"""
        if api_type == "replicate":
            info_text = """🎨 Replicate API - Image Generation

Purpose: Generate high-quality images using AI models like SDXL, Midjourney-style models

Where to get:
1. Go to https://replicate.com/
2. Sign up for an account
3. Go to Account → API tokens
4. Create a new token

Cost:
• Pay-per-use model
• ~$0.01-0.05 per image
• No monthly fees

Models available:
• SDXL (Stable Diffusion XL)
• Midjourney-style models
• Custom fine-tuned models

Note: This is OPTIONAL - the app works without it using basic image generation."""

        elif api_type == "stability":
            info_text = """🎨 Stability AI - Image Generation

Purpose: Generate images using Stable Diffusion models

Where to get:
1. Go to https://platform.stability.ai/
2. Sign up for an account
3. Go to API Keys section
4. Generate a new API key

Cost:
• Credit-based system
• ~$0.04 per image
• $10 minimum purchase

Models available:
• Stable Diffusion XL
• Stable Diffusion 3
• Various specialized models

Note: This is OPTIONAL - alternative to Replicate for image generation."""

        else:
            info_text = "API information not available."

        messagebox.showinfo(f"{api_type.title()} API Info", info_text)
