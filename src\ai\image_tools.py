"""
Midjourney Tools for TikTok Automation
Image description and generation via pi.ai API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
import json

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. Midjourney tools will not be available.")

class MidjourneyTools:
    """Midjourney integration via pi.ai API"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Midjourney tools
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['pi_ai']
        self.describe_enabled = config['ai_workflow']['midjourney']['describe_enabled']
        self.generation_enabled = config['ai_workflow']['midjourney']['generation_enabled']
        self.image_variants = config['ai_workflow']['midjourney']['image_variants']
        self.select_best = config['ai_workflow']['midjourney']['select_best']
        
        # API endpoints (these would be the actual pi.ai endpoints)
        self.base_url = "https://api.pi.ai/v1"
        self.describe_endpoint = f"{self.base_url}/midjourney/describe"
        self.generate_endpoint = f"{self.base_url}/midjourney/generate"
        
        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=120.0,  # Longer timeout for image generation
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )
    
    async def describe_image(self, image_url: str) -> Dict[str, Any]:
        """Describe an image using Midjourney's describe feature
        
        Args:
            image_url: Public URL of the image to describe
            
        Returns:
            Dict containing image description and metadata
        """
        if not self.describe_enabled:
            raise ValueError("Midjourney describe is disabled in configuration")
        
        if not self.api_key:
            raise ValueError("Pi.ai API key not configured")
        
        logging.info(f"Describing image via Midjourney: {image_url}")
        
        try:
            payload = {
                "image_url": image_url,
                "detail_level": "high",
                "include_style": True,
                "include_composition": True
            }
            
            response = await self.client.post(self.describe_endpoint, json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            if not result.get('success'):
                raise Exception(f"Midjourney describe failed: {result.get('error', 'Unknown error')}")
            
            description_data = {
                'description': result['data']['description'],
                'style_elements': result['data'].get('style_elements', []),
                'composition': result['data'].get('composition', ''),
                'color_palette': result['data'].get('color_palette', []),
                'mood': result['data'].get('mood', ''),
                'quality_score': result['data'].get('quality_score', 0),
                'timestamp': time.time()
            }
            
            logging.info(f"Image described successfully: {description_data['description'][:100]}...")
            return description_data
            
        except Exception as e:
            logging.error(f"Error describing image: {str(e)}")
            raise
    
    async def generate_image(self, prompt: str, style_reference: Optional[str] = None) -> Dict[str, Any]:
        """Generate image using Midjourney via pi.ai
        
        Args:
            prompt: Text prompt for image generation
            style_reference: Optional style reference URL
            
        Returns:
            Dict containing generated images and metadata
        """
        if not self.generation_enabled:
            raise ValueError("Midjourney generation is disabled in configuration")
        
        if not self.api_key:
            raise ValueError("Pi.ai API key not configured")
        
        logging.info(f"Generating image via Midjourney: {prompt[:100]}...")
        
        try:
            payload = {
                "prompt": prompt,
                "variants": self.image_variants,
                "quality": "high",
                "aspect_ratio": "9:16",  # TikTok format
                "style": "photorealistic"
            }
            
            if style_reference:
                payload["style_reference"] = style_reference
            
            response = await self.client.post(self.generate_endpoint, json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            if not result.get('success'):
                raise Exception(f"Midjourney generation failed: {result.get('error', 'Unknown error')}")
            
            # Process generated images
            images = result['data']['images']
            
            generation_data = {
                'prompt': prompt,
                'images': images,
                'best_image': None,
                'generation_id': result['data'].get('generation_id'),
                'timestamp': time.time()
            }
            
            # Select best image if enabled
            if self.select_best and len(images) > 1:
                generation_data['best_image'] = await self._select_best_image(images, prompt)
            else:
                generation_data['best_image'] = images[0] if images else None
            
            logging.info(f"Generated {len(images)} image variants successfully")
            return generation_data
            
        except Exception as e:
            logging.error(f"Error generating image: {str(e)}")
            raise
    
    async def _select_best_image(self, images: List[Dict], prompt: str) -> Dict[str, Any]:
        """Select the best image from generated variants
        
        Args:
            images: List of generated images
            prompt: Original prompt for context
            
        Returns:
            Dict: Best image data
        """
        # For now, use simple scoring based on image metadata
        # In a real implementation, this could use additional AI analysis
        
        best_image = images[0]
        best_score = 0
        
        for image in images:
            score = 0
            
            # Score based on quality metrics if available
            if 'quality_score' in image:
                score += image['quality_score'] * 0.4
            
            # Score based on resolution
            if 'width' in image and 'height' in image:
                aspect_ratio = image['height'] / image['width']
                # Prefer 9:16 aspect ratio for TikTok
                if 1.7 <= aspect_ratio <= 1.8:
                    score += 30
                elif 1.5 <= aspect_ratio <= 2.0:
                    score += 20
            
            # Score based on file size (reasonable size)
            if 'file_size' in image:
                size_mb = image['file_size'] / (1024 * 1024)
                if 1 <= size_mb <= 5:  # Good size range
                    score += 20
            
            if score > best_score:
                best_score = score
                best_image = image
        
        logging.info(f"Selected best image with score: {best_score}")
        return best_image
    
    async def download_image(self, image_url: str, output_path: str) -> str:
        """Download generated image to local file
        
        Args:
            image_url: URL of the image to download
            output_path: Local path to save the image
            
        Returns:
            str: Path to downloaded image
        """
        logging.info(f"Downloading image: {image_url}")
        
        try:
            response = await self.client.get(image_url)
            response.raise_for_status()
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save image
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"Image downloaded successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Error downloading image: {str(e)}")
            raise
    
    async def enhance_prompt_with_description(self, base_prompt: str, description: str, 
                                            niche: str) -> str:
        """Enhance image generation prompt using description
        
        Args:
            base_prompt: Base prompt for image generation
            description: Image description from describe API
            niche: Content niche for context
            
        Returns:
            str: Enhanced prompt
        """
        # Extract key visual elements from description
        visual_elements = self._extract_visual_elements(description)
        
        # Combine with base prompt and niche context
        enhanced_prompt = f"{base_prompt}, {visual_elements}, {niche} aesthetic, photorealistic, high quality, 9:16 aspect ratio, TikTok style"
        
        # Clean up and optimize prompt
        enhanced_prompt = self._optimize_prompt(enhanced_prompt)
        
        logging.info(f"Enhanced prompt: {enhanced_prompt}")
        return enhanced_prompt
    
    def _extract_visual_elements(self, description: str) -> str:
        """Extract key visual elements from image description
        
        Args:
            description: Image description text
            
        Returns:
            str: Extracted visual elements
        """
        # Simple keyword extraction - in production, this could be more sophisticated
        visual_keywords = []
        
        # Common visual descriptors
        descriptors = ['dark', 'bright', 'moody', 'atmospheric', 'dramatic', 'cinematic',
                      'detailed', 'realistic', 'stylized', 'vintage', 'modern', 'elegant']
        
        # Colors
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white',
                 'golden', 'silver', 'bronze', 'crimson', 'azure', 'emerald']
        
        # Lighting
        lighting = ['soft lighting', 'dramatic lighting', 'natural light', 'studio lighting',
                   'backlit', 'rim lighting', 'ambient light', 'harsh shadows']
        
        description_lower = description.lower()
        
        # Extract relevant keywords
        for keyword in descriptors + colors + lighting:
            if keyword in description_lower:
                visual_keywords.append(keyword)
        
        return ', '.join(visual_keywords[:5])  # Limit to top 5 elements
    
    def _optimize_prompt(self, prompt: str) -> str:
        """Optimize prompt for better generation results
        
        Args:
            prompt: Raw prompt text
            
        Returns:
            str: Optimized prompt
        """
        # Remove duplicates and clean up
        words = prompt.split(', ')
        unique_words = []
        seen = set()
        
        for word in words:
            word = word.strip()
            if word and word.lower() not in seen:
                unique_words.append(word)
                seen.add(word.lower())
        
        # Ensure key quality terms are included
        quality_terms = ['high quality', 'detailed', 'professional']
        for term in quality_terms:
            if not any(term in word.lower() for word in unique_words):
                unique_words.append(term)
        
        return ', '.join(unique_words)
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
