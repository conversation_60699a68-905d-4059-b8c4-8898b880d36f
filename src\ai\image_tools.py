"""
AI Image Tools for TikTok Automation
Image description and generation via Stability AI API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
import json
import base64

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. AI image tools will not be available.")

class MidjourneyTools:
    """AI image tools using Stability AI API"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize Midjourney tools

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys'].get('stability', '')
        self.describe_enabled = config['ai_workflow']['midjourney']['describe_enabled']
        self.generation_enabled = config['ai_workflow']['midjourney']['generation_enabled']
        self.image_variants = config['ai_workflow']['midjourney']['image_variants']
        self.select_best = config['ai_workflow']['midjourney']['select_best']

        # Stability AI API endpoints
        self.base_url = "https://api.stability.ai/v1"
        self.generate_endpoint = f"{self.base_url}/generation/stable-diffusion-xl-1024-v1-0/text-to-image"
        self.upscale_endpoint = f"{self.base_url}/generation/esrgan-v1-x2plus/image-to-image/upscale"

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=120.0,  # Longer timeout for image generation
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Accept": "application/json"
            }
        )

    async def describe_image(self, image_url: str) -> Dict[str, Any]:
        """Describe an image using basic analysis (fallback implementation)

        Args:
            image_url: Public URL of the image to describe

        Returns:
            Dict containing image description and metadata
        """
        logging.info(f"Generating basic image description for: {image_url}")

        # Since Stability AI doesn't have image description, provide a basic fallback
        # In a real implementation, you could use OpenAI Vision API or similar

        try:
            # Basic description based on common TikTok image elements
            description_data = {
                'description': 'A dramatic, atmospheric image with cinematic lighting and professional composition, suitable for viral TikTok content',
                'style_elements': ['cinematic', 'dramatic', 'atmospheric', 'professional'],
                'composition': 'Well-balanced composition with strong focal point',
                'color_palette': ['dramatic shadows', 'warm highlights', 'rich contrast'],
                'mood': 'engaging and mysterious',
                'quality_score': 85,
                'timestamp': time.time(),
                'provider': 'Basic Analysis (Fallback)'
            }

            logging.info(f"Basic image description generated successfully")
            return description_data

        except Exception as e:
            logging.error(f"Error in basic image description: {str(e)}")
            raise

    async def generate_image(self, prompt: str, style_reference: Optional[str] = None) -> Dict[str, Any]:
        """Generate image using Stability AI

        Args:
            prompt: Text prompt for image generation
            style_reference: Optional style reference (not used with Stability AI)

        Returns:
            Dict containing generated images and metadata
        """
        if not self.generation_enabled:
            raise ValueError("Image generation is disabled in configuration")

        if not self.api_key:
            raise ValueError("Stability AI API key not configured")

        logging.info(f"Generating image via Stability AI: {prompt[:100]}...")

        try:
            # Prepare payload for Stability AI (using supported dimensions)
            payload = {
                "text_prompts": [
                    {
                        "text": f"anime style, {prompt}, cinematic, dramatic lighting, high quality, detailed",
                        "weight": 1.0
                    }
                ],
                "cfg_scale": 7,
                "height": 1344,  # Closest to 9:16 ratio: 768x1344
                "width": 768,
                "samples": 1,  # Start with 1 sample to avoid quota issues
                "steps": 30,
                "style_preset": "anime"
            }

            # Make request to Stability AI with proper headers
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            response = await self.client.post(
                self.generate_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()

            result = response.json()

            # Process Stability AI response
            images = []
            for i, artifact in enumerate(result.get('artifacts', [])):
                if artifact['finishReason'] == 'SUCCESS':
                    images.append({
                        'url': f"data:image/png;base64,{artifact['base64']}",
                        'base64': artifact['base64'],
                        'seed': artifact['seed'],
                        'index': i,
                        'quality_score': 90  # Default high score for Stability AI
                    })

            generation_data = {
                'prompt': prompt,
                'images': images,
                'best_image': None,
                'generation_id': f"stability_{int(time.time())}",
                'provider': 'Stability AI',
                'timestamp': time.time()
            }

            # Select best image if enabled
            if self.select_best and len(images) > 1:
                generation_data['best_image'] = await self._select_best_image(images, prompt)
            else:
                generation_data['best_image'] = images[0] if images else None

            logging.info(f"Generated {len(images)} image variants successfully with Stability AI")
            return generation_data

        except Exception as e:
            logging.error(f"Error generating image with Stability AI: {str(e)}")
            raise

    async def _select_best_image(self, images: List[Dict], prompt: str) -> Dict[str, Any]:
        """Select the best image from generated variants

        Args:
            images: List of generated images
            prompt: Original prompt for context

        Returns:
            Dict: Best image data
        """
        # For now, use simple scoring based on image metadata
        # In a real implementation, this could use additional AI analysis

        best_image = images[0]
        best_score = 0

        for image in images:
            score = 0

            # Score based on quality metrics if available
            if 'quality_score' in image:
                score += image['quality_score'] * 0.4

            # Score based on resolution
            if 'width' in image and 'height' in image:
                aspect_ratio = image['height'] / image['width']
                # Prefer 9:16 aspect ratio for TikTok
                if 1.7 <= aspect_ratio <= 1.8:
                    score += 30
                elif 1.5 <= aspect_ratio <= 2.0:
                    score += 20

            # Score based on file size (reasonable size)
            if 'file_size' in image:
                size_mb = image['file_size'] / (1024 * 1024)
                if 1 <= size_mb <= 5:  # Good size range
                    score += 20

            if score > best_score:
                best_score = score
                best_image = image

        logging.info(f"Selected best image with score: {best_score}")
        return best_image

    async def download_image(self, image_data: Dict[str, Any], output_path: str) -> str:
        """Download/save generated image to local file

        Args:
            image_data: Image data dict (can contain URL or base64)
            output_path: Local path to save the image

        Returns:
            str: Path to downloaded image
        """
        logging.info(f"Saving image to: {output_path}")

        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Handle different image data formats
            if 'base64' in image_data:
                # Stability AI returns base64 data
                image_bytes = base64.b64decode(image_data['base64'])
                with open(output_path, 'wb') as f:
                    f.write(image_bytes)
            elif 'url' in image_data and image_data['url'].startswith('http'):
                # Regular URL download
                response = await self.client.get(image_data['url'])
                response.raise_for_status()
                with open(output_path, 'wb') as f:
                    f.write(response.content)
            elif 'url' in image_data and image_data['url'].startswith('data:image'):
                # Data URL (base64 embedded)
                header, data = image_data['url'].split(',', 1)
                image_bytes = base64.b64decode(data)
                with open(output_path, 'wb') as f:
                    f.write(image_bytes)
            else:
                raise ValueError("Invalid image data format")

            logging.info(f"Image saved successfully: {output_path}")
            return output_path

        except Exception as e:
            logging.error(f"Error saving image: {str(e)}")
            raise

    async def enhance_prompt_with_description(self, base_prompt: str, description: str,
                                            niche: str) -> str:
        """Enhance image generation prompt using description

        Args:
            base_prompt: Base prompt for image generation
            description: Image description from describe API
            niche: Content niche for context

        Returns:
            str: Enhanced prompt
        """
        # Extract key visual elements from description
        visual_elements = self._extract_visual_elements(description)

        # Combine with base prompt and niche context
        enhanced_prompt = f"{base_prompt}, {visual_elements}, {niche} aesthetic, photorealistic, high quality, 9:16 aspect ratio, TikTok style"

        # Clean up and optimize prompt
        enhanced_prompt = self._optimize_prompt(enhanced_prompt)

        logging.info(f"Enhanced prompt: {enhanced_prompt}")
        return enhanced_prompt

    def _extract_visual_elements(self, description: str) -> str:
        """Extract key visual elements from image description

        Args:
            description: Image description text

        Returns:
            str: Extracted visual elements
        """
        # Simple keyword extraction - in production, this could be more sophisticated
        visual_keywords = []

        # Common visual descriptors
        descriptors = ['dark', 'bright', 'moody', 'atmospheric', 'dramatic', 'cinematic',
                      'detailed', 'realistic', 'stylized', 'vintage', 'modern', 'elegant']

        # Colors
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white',
                 'golden', 'silver', 'bronze', 'crimson', 'azure', 'emerald']

        # Lighting
        lighting = ['soft lighting', 'dramatic lighting', 'natural light', 'studio lighting',
                   'backlit', 'rim lighting', 'ambient light', 'harsh shadows']

        description_lower = description.lower()

        # Extract relevant keywords
        for keyword in descriptors + colors + lighting:
            if keyword in description_lower:
                visual_keywords.append(keyword)

        return ', '.join(visual_keywords[:5])  # Limit to top 5 elements

    def _optimize_prompt(self, prompt: str) -> str:
        """Optimize prompt for better generation results

        Args:
            prompt: Raw prompt text

        Returns:
            str: Optimized prompt
        """
        # Remove duplicates and clean up
        words = prompt.split(', ')
        unique_words = []
        seen = set()

        for word in words:
            word = word.strip()
            if word and word.lower() not in seen:
                unique_words.append(word)
                seen.add(word.lower())

        # Ensure key quality terms are included
        quality_terms = ['high quality', 'detailed', 'professional']
        for term in quality_terms:
            if not any(term in word.lower() for word in unique_words):
                unique_words.append(term)

        return ', '.join(unique_words)

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
