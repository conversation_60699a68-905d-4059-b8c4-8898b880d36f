"""
Hedra Video Assembler for TikTok Automation
Animated video generation using Hedra API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional
import json

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. Hedra video assembler will not be available.")

class HedraVideoAssembler:
    """Hedra API integration for video assembly"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Hedra video assembler
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['hedra']
        self.enabled = config['ai_workflow']['hedra_video']['enabled']
        self.quality = config['ai_workflow']['hedra_video']['quality']
        self.aspect_ratio = config['ai_workflow']['hedra_video']['aspect_ratio']
        
        # API endpoints
        self.base_url = "https://api.hedra.com/v1"
        self.generate_endpoint = f"{self.base_url}/generate"
        self.status_endpoint = f"{self.base_url}/status"
        
        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=300.0,  # 5 minutes for video generation
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )
        
        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "videos")
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def generate_video(self, image_path: str, audio_path: str, 
                           output_filename: Optional[str] = None,
                           animation_style: str = "natural") -> Dict[str, Any]:
        """Generate animated video using Hedra API
        
        Args:
            image_path: Path to the source image
            audio_path: Path to the audio file
            output_filename: Optional filename for output
            animation_style: Animation style (natural, dramatic, subtle)
            
        Returns:
            Dict containing video generation result
        """
        if not self.enabled:
            raise ValueError("Hedra video generation is disabled in configuration")
        
        if not self.api_key:
            raise ValueError("Hedra API key not configured")
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        logging.info(f"Generating video with Hedra: {image_path} + {audio_path}")
        
        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"video_{timestamp}.mp4"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        try:
            # Upload files and start generation
            generation_id = await self._start_generation(image_path, audio_path, animation_style)
            
            # Poll for completion
            video_url = await self._wait_for_completion(generation_id)
            
            # Download the generated video
            await self._download_video(video_url, output_path)
            
            result = {
                'path': output_path,
                'generation_id': generation_id,
                'animation_style': animation_style,
                'quality': self.quality,
                'aspect_ratio': self.aspect_ratio,
                'timestamp': time.time()
            }
            
            logging.info(f"Video generated successfully: {output_path}")
            return result
            
        except Exception as e:
            logging.error(f"Error generating video with Hedra: {str(e)}")
            raise
    
    async def _start_generation(self, image_path: str, audio_path: str, 
                              animation_style: str) -> str:
        """Start video generation process
        
        Args:
            image_path: Path to source image
            audio_path: Path to audio file
            animation_style: Animation style
            
        Returns:
            str: Generation ID
        """
        try:
            # Prepare files for upload
            with open(image_path, 'rb') as image_file, open(audio_path, 'rb') as audio_file:
                files = {
                    'image': image_file,
                    'audio': audio_file
                }
                
                data = {
                    'animation_style': animation_style,
                    'quality': self.quality,
                    'aspect_ratio': self.aspect_ratio,
                    'format': 'mp4'
                }
                
                response = await self.client.post(
                    self.generate_endpoint,
                    files=files,
                    data=data
                )
                response.raise_for_status()
            
            result = response.json()
            
            if not result.get('success'):
                raise Exception(f"Hedra generation start failed: {result.get('error', 'Unknown error')}")
            
            generation_id = result['data']['generation_id']
            logging.info(f"Hedra generation started: {generation_id}")
            
            return generation_id
            
        except Exception as e:
            logging.error(f"Error starting Hedra generation: {str(e)}")
            raise
    
    async def _wait_for_completion(self, generation_id: str, 
                                 max_wait_time: int = 300) -> str:
        """Wait for video generation to complete
        
        Args:
            generation_id: Generation ID to monitor
            max_wait_time: Maximum wait time in seconds
            
        Returns:
            str: URL of generated video
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                response = await self.client.get(f"{self.status_endpoint}/{generation_id}")
                response.raise_for_status()
                
                result = response.json()
                
                if not result.get('success'):
                    raise Exception(f"Status check failed: {result.get('error', 'Unknown error')}")
                
                status = result['data']['status']
                progress = result['data'].get('progress', 0)
                
                logging.info(f"Generation {generation_id}: {status} ({progress}%)")
                
                if status == 'completed':
                    return result['data']['video_url']
                elif status == 'failed':
                    error_msg = result['data'].get('error', 'Generation failed')
                    raise Exception(f"Hedra generation failed: {error_msg}")
                
                # Wait before next check
                await asyncio.sleep(10)
                
            except Exception as e:
                logging.error(f"Error checking generation status: {str(e)}")
                raise
        
        raise TimeoutError(f"Video generation timed out after {max_wait_time} seconds")
    
    async def _download_video(self, video_url: str, output_path: str) -> None:
        """Download generated video
        
        Args:
            video_url: URL of the generated video
            output_path: Local path to save the video
        """
        try:
            response = await self.client.get(video_url)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"Video downloaded: {output_path}")
            
        except Exception as e:
            logging.error(f"Error downloading video: {str(e)}")
            raise
    
    async def get_generation_status(self, generation_id: str) -> Dict[str, Any]:
        """Get status of a video generation
        
        Args:
            generation_id: Generation ID to check
            
        Returns:
            Dict: Status information
        """
        try:
            response = await self.client.get(f"{self.status_endpoint}/{generation_id}")
            response.raise_for_status()
            
            result = response.json()
            
            if not result.get('success'):
                raise Exception(f"Status check failed: {result.get('error', 'Unknown error')}")
            
            return result['data']
            
        except Exception as e:
            logging.error(f"Error getting generation status: {str(e)}")
            raise
    
    async def cancel_generation(self, generation_id: str) -> bool:
        """Cancel a video generation
        
        Args:
            generation_id: Generation ID to cancel
            
        Returns:
            bool: True if successful
        """
        try:
            response = await self.client.delete(f"{self.generate_endpoint}/{generation_id}")
            response.raise_for_status()
            
            result = response.json()
            success = result.get('success', False)
            
            if success:
                logging.info(f"Generation cancelled: {generation_id}")
            
            return success
            
        except Exception as e:
            logging.warning(f"Error cancelling generation: {str(e)}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """Get supported video formats
        
        Returns:
            List: Supported formats
        """
        return ['mp4', 'mov', 'avi']
    
    def get_supported_aspect_ratios(self) -> List[str]:
        """Get supported aspect ratios
        
        Returns:
            List: Supported aspect ratios
        """
        return ['9:16', '16:9', '1:1', '4:5']
    
    def get_animation_styles(self) -> List[str]:
        """Get available animation styles
        
        Returns:
            List: Available animation styles
        """
        return ['natural', 'dramatic', 'subtle', 'energetic', 'calm']
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
