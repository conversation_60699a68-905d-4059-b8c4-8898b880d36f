"""
Enhanced Video Generation Pipeline for TikTok Automation
Orchestrates the complete AI workflow from story to final video
"""

import os
import logging
import time
import async<PERSON>
from typing import Dict, Any, Optional, Callable
import json

from src.ai.story_generator import EnhancedStoryGenerator
from src.ai.image_tools import MidjourneyTools
from src.ai.audio_tools import ElevenLabsAudio
from src.ai.enhanced_video_assembler import EnhancedVideoAssembler
from src.ai.image_hosting import ImageHostingService

class VideoGenerationPipeline:
    """Enhanced video generation pipeline with AI workflow"""

    def __init__(self, config: Dict[str, Any], db_manager, status_callback: Optional[Callable] = None):
        """Initialize the video generation pipeline

        Args:
            config: Configuration dictionary
            db_manager: Database manager instance
            status_callback: Optional callback for status updates
        """
        self.config = config
        self.db_manager = db_manager
        self.status_callback = status_callback or (lambda x: None)

        # Initialize AI components
        self.story_generator = EnhancedStoryGenerator(config)
        self.midjourney_tools = MidjourneyTools(config)
        self.audio_tools = ElevenLabsAudio(config)
        self.video_assembler = EnhancedVideoAssembler(config)
        self.image_hosting = ImageHostingService(config)

        # Pipeline state
        self.current_generation = None
        self.generation_history = []

        # Output directories
        self.base_output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "output")
        os.makedirs(self.base_output_dir, exist_ok=True)

    async def generate_video(self, account: Dict[str, Any],
                           base_image_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate a complete video using the enhanced AI workflow

        Args:
            account: Account configuration
            base_image_path: Optional base image for enhancement

        Returns:
            Dict: Generation result with all file paths and metadata
        """
        generation_id = f"gen_{int(time.time())}"
        self.current_generation = generation_id

        logging.info(f"Starting enhanced video generation for account: {account['username']}")
        self.status_callback(f"Starting video generation for {account['username']}")

        try:
            # Create account output directory
            account_output_dir = os.path.join(self.base_output_dir, account['username'])
            os.makedirs(account_output_dir, exist_ok=True)

            # Step 1: Generate viral story
            self.status_callback("Step 1/9: Generating viral story...")
            story_result = await self.story_generator.generate_viral_story(
                niche=account['niche'],
                duration_seconds=account['video_duration']
            )

            # Step 2: Generate audio from story
            self.status_callback("Step 2/9: Generating audio...")
            audio_path = await self.audio_tools.generate_speech(
                text=story_result['text'],
                voice_type=account['voice_type']
            )

            # Step 3: Handle base image (generate or use provided)
            self.status_callback("Step 3/9: Preparing base image...")
            if not base_image_path:
                # Generate a basic image using existing image generator
                from src.image_generation.image_generator import ImageGenerator
                basic_generator = ImageGenerator(self.config)
                base_image_path = basic_generator.generate_image(
                    prompt=story_result['title'],
                    niche=account['niche']
                )

            # Step 4: Upload image to temporary hosting
            self.status_callback("Step 4/9: Uploading image for analysis...")
            upload_result = await self.image_hosting.upload_image(base_image_path)
            image_url = upload_result['url']

            # Step 5: Analyze image with Midjourney Describe
            self.status_callback("Step 5/9: Analyzing image with AI...")
            try:
                description_result = await self.midjourney_tools.describe_image(image_url)
                image_description = description_result['description']
            except Exception as e:
                logging.warning(f"Image description failed, using fallback: {str(e)}")
                image_description = f"A {account['niche']} themed image with atmospheric lighting"

            # Step 6: Refine prompt using GPT + image description
            self.status_callback("Step 6/9: Refining image prompt...")
            base_prompt = f"{story_result['title']}, {account['niche']} style"
            refined_prompt = self.story_generator.refine_prompt_with_image_description(
                base_prompt=base_prompt,
                image_description=image_description,
                niche=account['niche']
            )

            # Step 7: Generate enhanced image with Midjourney
            self.status_callback("Step 7/9: Generating enhanced image...")
            try:
                generation_result = await self.midjourney_tools.generate_image(refined_prompt)
                best_image = generation_result['best_image']

                # Download the enhanced image
                enhanced_image_filename = f"enhanced_image_{generation_id}.jpg"
                enhanced_image_path = os.path.join(account_output_dir, enhanced_image_filename)
                await self.midjourney_tools.download_image(best_image, enhanced_image_path)

            except Exception as e:
                logging.warning(f"Enhanced image generation failed, using base image: {str(e)}")
                enhanced_image_path = base_image_path

            # Step 8: Generate final video with enhanced effects
            self.status_callback("Step 8/9: Assembling final video...")
            try:
                video_result = await self.video_assembler.generate_video(
                    image_path=enhanced_image_path,
                    audio_path=audio_path,
                    output_filename=f"final_video_{generation_id}.mp4",
                    animation_style="dramatic",  # Use dramatic animation for anime effect
                    story_text=story_result['text']  # Pass story text for subtitles
                )
                final_video_path = video_result['path']

            except Exception as e:
                logging.warning(f"Hedra video generation failed, using fallback: {str(e)}")
                # Fallback to existing video generator
                from src.video_generation.video_generator import VideoGenerator
                fallback_generator = VideoGenerator(self.config)
                fallback_result = fallback_generator.generate_video(
                    story_text=story_result['text'],
                    audio_path=audio_path,
                    image_path=enhanced_image_path
                )
                final_video_path = fallback_result['path']

            # Step 9: Save to database and finalize
            self.status_callback("Step 9/9: Saving to database...")

            # Move final video to account directory
            final_output_path = os.path.join(account_output_dir, f"video_{generation_id}.mp4")
            if final_video_path != final_output_path:
                import shutil
                shutil.move(final_video_path, final_output_path)

            # Also copy audio and image files to output folder
            try:
                audio_output_path = os.path.join(account_output_dir, f"audio_{generation_id}.mp3")
                shutil.copy2(audio_path, audio_output_path)
                logging.info(f"Audio copied to output: {audio_output_path}")

                image_output_path = os.path.join(account_output_dir, f"image_{generation_id}.png")
                shutil.copy2(enhanced_image_path, image_output_path)
                logging.info(f"Image copied to output: {image_output_path}")
            except Exception as e:
                logging.warning(f"Failed to copy audio/image to output: {str(e)}")

            # Save to database
            video_data = {
                'account_id': account['id'],
                'title': story_result['title'],
                'story_text': story_result['text'],
                'audio_path': audio_path,
                'image_path': enhanced_image_path,
                'video_path': final_output_path,
                'status': 'generated'
            }

            video_id = self.db_manager.add_video(video_data)

            # Cleanup temporary hosted image
            try:
                await self.image_hosting.delete_image(upload_result['delete_url'])
            except:
                pass  # Non-critical cleanup

            # Prepare final result
            result = {
                'video_id': video_id,
                'generation_id': generation_id,
                'account': account,
                'story': story_result,
                'audio_path': audio_path,
                'image_path': enhanced_image_path,
                'video_path': final_output_path,
                'viral_score': story_result.get('viral_score', 0),
                'generation_time': time.time(),
                'success': True
            }

            self.generation_history.append(result)
            self.status_callback(f"Video generation completed successfully!")

            logging.info(f"Enhanced video generation completed: {final_output_path}")
            return result

        except Exception as e:
            error_msg = f"Video generation failed: {str(e)}"
            logging.error(error_msg, exc_info=True)
            self.status_callback(error_msg)

            # Return error result
            return {
                'generation_id': generation_id,
                'account': account,
                'success': False,
                'error': str(e),
                'generation_time': time.time()
            }

        finally:
            self.current_generation = None

    async def generate_batch(self, accounts: list, max_concurrent: int = 2) -> list:
        """Generate videos for multiple accounts concurrently

        Args:
            accounts: List of account configurations
            max_concurrent: Maximum concurrent generations

        Returns:
            List: Generation results
        """
        logging.info(f"Starting batch generation for {len(accounts)} accounts")
        self.status_callback(f"Starting batch generation for {len(accounts)} accounts")

        # Create semaphore to limit concurrent generations
        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_with_semaphore(account):
            async with semaphore:
                return await self.generate_video(account)

        # Run generations concurrently
        tasks = [generate_with_semaphore(account) for account in accounts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        successful = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
        failed = len(results) - successful

        self.status_callback(f"Batch generation completed: {successful} successful, {failed} failed")

        return results

    def get_generation_stats(self) -> Dict[str, Any]:
        """Get generation statistics

        Returns:
            Dict: Generation statistics
        """
        if not self.generation_history:
            return {'total': 0, 'successful': 0, 'failed': 0, 'avg_viral_score': 0}

        total = len(self.generation_history)
        successful = sum(1 for g in self.generation_history if g.get('success'))
        failed = total - successful

        viral_scores = [g.get('viral_score', 0) for g in self.generation_history if g.get('success')]
        avg_viral_score = sum(viral_scores) / len(viral_scores) if viral_scores else 0

        return {
            'total': total,
            'successful': successful,
            'failed': failed,
            'success_rate': (successful / total) * 100 if total > 0 else 0,
            'avg_viral_score': avg_viral_score
        }

    async def cleanup_resources(self):
        """Cleanup pipeline resources"""
        try:
            await self.midjourney_tools.close()
            await self.audio_tools.close()
            await self.video_assembler.close()
            await self.image_hosting.close()
        except Exception as e:
            logging.warning(f"Error during cleanup: {str(e)}")

    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.cleanup_resources())
        except:
            pass
