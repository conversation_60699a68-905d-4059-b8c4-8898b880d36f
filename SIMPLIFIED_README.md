# 🎬 Simplified TikTok Automation Pipeline

A clean, production-ready Python automation system for generating TikTok videos using only the best AI tools.

## 🧰 **Tools Used (Only These 4)**

1. **OpenAI (GPT-4)** - Script generation & prompt enhancement
2. **ApyHub** - Text-to-audio conversion (TTS)
3. **PiAPI** - Midjourney image generation & description
4. **Hedra** - Audio + image → animated video

## 🚀 **Quick Start**

### 1. Installation
```bash
pip install -r simplified_requirements.txt
```

### 2. Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
OPENAI_API_KEY=your-key-here
APYHUB_API_KEY=your-key-here
PIAPI_API_KEY=your-key-here
HEDRA_API_KEY=your-key-here
```

### 3. Basic Usage
```python
import asyncio
from simplified_tiktok_automation import APIConfig, VideoConfig, TikTokPipeline

async def generate_video():
    # Configure APIs
    api_config = APIConfig.from_env()  # Load from .env
    video_config = VideoConfig(niche="scary_stories")
    
    # Generate video
    async with TikTokPipeline(api_config, video_config) as pipeline:
        result = await pipeline.generate_video()
        print(f"Video generated: {result['video_path']}")

asyncio.run(generate_video())
```

## 📋 **Pipeline Steps**

1. **📝 Story Generation** - OpenAI creates viral TikTok script
2. **🎙️ Audio Creation** - ApyHub converts script to speech
3. **✨ Prompt Enhancement** - OpenAI optimizes visual prompts
4. **🎨 Image Generation** - PiAPI/Midjourney creates visuals
5. **🎬 Video Assembly** - Hedra combines audio + image
6. **💾 Export** - Final MP4 in TikTok format (9:16)

## 🎯 **Features**

### **Core Generation**
- ✅ Single video generation
- ✅ Batch video generation
- ✅ Custom prompt support
- ✅ Multiple voice options
- ✅ Baby transformation mode

### **Advanced Features**
- ✅ Image description & recreation
- ✅ Automatic duration optimization
- ✅ TikTok format optimization (9:16)
- ✅ Production-ready error handling
- ✅ Async/await throughout

### **Content Types**
- 🎭 Scary stories
- 🔍 Mystery content
- 👻 Horror narratives
- 🕵️ True crime
- 🌙 Supernatural tales

## 📚 **API Documentation**

### **Generate Single Video**
```python
result = await pipeline.generate_video(
    niche="scary_stories",           # Content type
    voice="en-US-AriaNeural",       # TTS voice
    transformation_type="normal"     # normal/baby/enhanced
)
```

### **Batch Generation**
```python
results = await pipeline.generate_batch(
    count=5,                        # Number of videos
    niche="mystery",               # Content type
    delay_between=30               # Seconds between generations
)
```

### **Custom Prompt**
```python
result = await pipeline.generate_video(
    custom_prompt="A mysterious figure walks through fog...",
    voice="en-US-DavisNeural"
)
```

### **Describe & Recreate**
```python
result = await pipeline.describe_and_recreate(
    image_url="https://example.com/image.jpg",
    voice="en-US-JennyNeural"
)
```

## 🎙️ **Available Voices**

| Voice | Type | Description |
|-------|------|-------------|
| `en-US-AriaNeural` | Female | Clear, professional |
| `en-US-DavisNeural` | Male | Professional, authoritative |
| `en-US-JennyNeural` | Female | Friendly, engaging |
| `en-US-GuyNeural` | Male | Casual, relatable |
| `en-US-AmberNeural` | Female | Warm, storytelling |
| `en-US-AnaNeural` | Female | Young, energetic |
| `en-US-BrandonNeural` | Male | Energetic, dynamic |
| `en-US-ChristopherNeural` | Male | Deep, dramatic |
| `en-US-CoraNeural` | Female | Professional, clear |
| `en-US-ElizabethNeural` | Female | Mature, authoritative |

## 📁 **Project Structure**

```
simplified_tiktok_automation/
├── config.py              # Configuration classes
├── pipeline.py             # Main pipeline orchestrator
├── services/
│   ├── openai_service.py   # OpenAI GPT-4 integration
│   ├── apyhub_service.py   # ApyHub TTS integration
│   ├── piapi_service.py    # PiAPI Midjourney integration
│   └── hedra_service.py    # Hedra video generation
├── __init__.py
└── services/__init__.py

example_usage.py            # Complete usage examples
.env.example               # Environment template
simplified_requirements.txt # Dependencies
```

## 🔧 **Configuration Options**

### **API Configuration**
```python
api_config = APIConfig(
    openai_api_key="sk-...",
    apyhub_api_key="apy-...",
    piapi_api_key="pi-...",
    hedra_api_key="hd-..."
)
```

### **Video Configuration**
```python
video_config = VideoConfig(
    width=1080,                # TikTok width
    height=1920,               # TikTok height (9:16)
    max_duration=60,           # Max seconds
    script_length=60,          # Target word count
    niche="scary_stories",     # Content type
    output_dir="output",       # Output directory
    audio_quality="high",      # Audio quality
    video_quality="high"       # Video quality
)
```

## 🎯 **Production Tips**

### **Rate Limiting**
- Use `delay_between` in batch generation
- Monitor API quotas
- Implement exponential backoff

### **Error Handling**
- All services include comprehensive error handling
- Automatic retries for transient failures
- Graceful degradation when possible

### **Performance**
- Async/await throughout for efficiency
- Parallel processing where possible
- Optimized file handling

### **Cost Optimization**
- Text optimization for duration limits
- Efficient prompt engineering
- Batch processing for better rates

## 📊 **Expected Costs (Approximate)**

| Service | Cost per Video | Notes |
|---------|---------------|-------|
| OpenAI | $0.01-0.03 | Script + prompt enhancement |
| ApyHub | $0.02-0.05 | Text-to-speech conversion |
| PiAPI | $0.10-0.20 | Midjourney image generation |
| Hedra | $0.20-0.50 | Video generation |
| **Total** | **$0.33-0.78** | Per 60-second video |

## 🔗 **API Documentation Links**

- [OpenAI API](https://platform.openai.com/docs)
- [ApyHub Text-to-Audio](https://apyhub.com/utility/convert-text-to-audio)
- [PiAPI Midjourney](https://piapi.ai/docs/midjourney-api)
- [Hedra Video Generation](https://api.hedra.com/web-app/redoc)

---

**🎬 Ready to create viral TikTok content with AI!**
