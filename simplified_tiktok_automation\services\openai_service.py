"""
OpenAI Service for Script Generation and Prompt Enhancement
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
import httpx
from ..config import APIConfig, PromptTemplates

class OpenAIService:
    """Service for OpenAI GPT-4 interactions"""
    
    def __init__(self, config: APIConfig):
        self.config = config
        self.client = httpx.AsyncClient(
            timeout=60.0,
            headers={
                "Authorization": f"Bearer {config.openai_api_key}",
                "Content-Type": "application/json"
            }
        )
        self.endpoint = "https://api.openai.com/v1/chat/completions"
        
    async def generate_story(self, niche: str = "scary_stories", word_count: int = 60) -> Dict[str, Any]:
        """Generate a TikTok story script
        
        Args:
            niche: Content niche (scary_stories, mystery, etc.)
            word_count: Target word count for script
            
        Returns:
            Dict containing story data
        """
        logging.info(f"Generating {niche} story ({word_count} words)")
        
        prompt = PromptTemplates.STORY_GENERATION.format(
            niche=niche.replace("_", " "),
            word_count=word_count
        )
        
        payload = {
            "model": self.config.openai_model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert TikTok content creator specializing in viral short-form videos."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.8,
            "max_tokens": 1000
        }
        
        try:
            response = await self.client.post(self.endpoint, json=payload)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # Parse JSON response
            try:
                story_data = json.loads(content)
                logging.info(f"Generated story: {story_data.get('title', 'Untitled')}")
                return story_data
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "title": "Generated Story",
                    "script": content,
                    "hook": content.split('.')[0] if '.' in content else content[:50],
                    "visual_scene": "A dramatic scene matching the story mood",
                    "mood": "mysterious",
                    "duration_estimate": "60"
                }
                
        except Exception as e:
            logging.error(f"Error generating story: {str(e)}")
            raise
    
    async def enhance_visual_prompt(self, scene_description: str, mood: str = "dramatic") -> str:
        """Enhance a scene description into a Midjourney prompt
        
        Args:
            scene_description: Basic scene description
            mood: Desired mood/atmosphere
            
        Returns:
            Enhanced Midjourney prompt
        """
        logging.info("Enhancing visual prompt with OpenAI")
        
        prompt = PromptTemplates.VISUAL_ENHANCEMENT.format(
            scene_description=scene_description,
            mood=mood
        )
        
        payload = {
            "model": self.config.openai_model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert prompt engineer for AI image generation."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 300
        }
        
        try:
            response = await self.client.post(self.endpoint, json=payload)
            response.raise_for_status()
            
            result = response.json()
            enhanced_prompt = result['choices'][0]['message']['content'].strip()
            
            logging.info(f"Enhanced prompt: {enhanced_prompt[:100]}...")
            return enhanced_prompt
            
        except Exception as e:
            logging.error(f"Error enhancing prompt: {str(e)}")
            # Return original description as fallback
            return f"photorealistic, {scene_description}, {mood} lighting, cinematic composition, high quality"
    
    async def create_baby_transformation_prompt(self, scene_description: str) -> str:
        """Create baby transformation prompt
        
        Args:
            scene_description: Original scene description
            
        Returns:
            Baby transformation prompt
        """
        logging.info("Creating baby transformation prompt")
        
        prompt = PromptTemplates.BABY_TRANSFORMATION.format(
            scene_description=scene_description
        )
        
        payload = {
            "model": self.config.openai_model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an advanced visual transformation AI specialized in photorealistic baby transformations."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.6,
            "max_tokens": 400
        }
        
        try:
            response = await self.client.post(self.endpoint, json=payload)
            response.raise_for_status()
            
            result = response.json()
            baby_prompt = result['choices'][0]['message']['content'].strip()
            
            # Remove any line breaks as specified
            baby_prompt = baby_prompt.replace('\n', ' ').replace('\\n', ' ')
            
            logging.info(f"Baby transformation prompt created: {len(baby_prompt)} chars")
            return baby_prompt
            
        except Exception as e:
            logging.error(f"Error creating baby transformation: {str(e)}")
            return f"photorealistic baby version of {scene_description}, maintaining exact pose and environment"
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
