"""
Video management frame for TikTok Automation
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Dict, Any, Optional, List, Callable
import logging
import threading
import time
import subprocess
from PIL import Image, ImageTk

from src.database.db_manager import DatabaseManager
from src.story_generation.story_generator import StoryGenerator
from src.tts.speech_generator import SpeechGenerator
from src.image_generation.image_generator import ImageGenerator
from src.video_generation.video_generator import VideoGenerator
from src.export.export_manager import ExportManager
# Enhanced AI workflow imports
from src.core.pipeline import VideoGenerationPipeline
from src.core.account_manager import EnhancedAccountManager
from src.core.scheduler import AutoGenerationScheduler

class VideoFrame(ttk.Frame):
    """Frame for managing TikTok video generation"""

    def __init__(self, parent, db_manager: DatabaseManager, config: Dict[str, Any],
                 status_callback: Callable[[str], None]):
        """Initialize video frame

        Args:
            parent: Parent widget
            db_manager: Database manager instance
            config: Application configuration
            status_callback: Callback function for status updates
        """
        super().__init__(parent)
        self.db_manager = db_manager
        self.config = config
        self.set_status = status_callback

        # Initialize components
        self.story_generator = None
        self.speech_generator = None
        self.image_generator = None
        self.video_generator = None
        self.export_manager = None

        # Enhanced AI workflow components
        self.enhanced_pipeline = None
        self.enhanced_account_manager = None
        self.auto_scheduler = None
        self.use_enhanced_workflow = True  # Toggle for enhanced vs legacy workflow

        # Create UI elements
        self.create_widgets()

        # Initialize enhanced components
        self._initialize_enhanced_components()

        # Load accounts and videos
        self.refresh_accounts()
        self.refresh_videos()

    def create_widgets(self):
        """Create UI widgets"""
        # Create main layout frames
        self.left_frame = ttk.Frame(self)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.right_frame = ttk.Frame(self)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create account selector
        self.create_account_selector()

        # Create video list
        self.create_video_list()

        # Create generation controls
        self.create_generation_controls()

        # Create preview area
        self.create_preview_area()

        # Create action buttons
        self.create_action_buttons()

        # Add scheduler controls if enhanced workflow is available
        if self.enhanced_pipeline:
            self.add_scheduler_controls()

    def create_account_selector(self):
        """Create account selector widget"""
        selector_frame = ttk.LabelFrame(self.left_frame, text="Select Account")
        selector_frame.pack(fill=tk.X, padx=5, pady=5)

        # Account dropdown
        ttk.Label(selector_frame, text="Account:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.account_var = tk.StringVar()
        self.account_combo = ttk.Combobox(selector_frame, textvariable=self.account_var, width=30)
        self.account_combo.grid(row=0, column=1, padx=5, pady=5)
        self.account_combo.bind("<<ComboboxSelected>>", self.on_account_selected)

        # Account info
        info_frame = ttk.Frame(selector_frame)
        info_frame.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        ttk.Label(info_frame, text="Niche:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.niche_label = ttk.Label(info_frame, text="-")
        self.niche_label.grid(row=0, column=1, sticky=tk.W, padx=5)

        ttk.Label(info_frame, text="Duration:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.duration_label = ttk.Label(info_frame, text="-")
        self.duration_label.grid(row=0, column=3, sticky=tk.W, padx=5)

        ttk.Label(info_frame, text="Voice:").grid(row=1, column=0, sticky=tk.W, padx=5)
        self.voice_label = ttk.Label(info_frame, text="-")
        self.voice_label.grid(row=1, column=1, sticky=tk.W, padx=5)

        ttk.Label(info_frame, text="Status:").grid(row=1, column=2, sticky=tk.W, padx=5)
        self.status_label = ttk.Label(info_frame, text="-")
        self.status_label.grid(row=1, column=3, sticky=tk.W, padx=5)

    def create_video_list(self):
        """Create video list widget"""
        list_frame = ttk.LabelFrame(self.left_frame, text="Generated Videos")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview with scrollbar
        columns = ("id", "title", "status", "created_at")
        self.video_tree = ttk.Treeview(list_frame, columns=columns, show="headings")

        # Define column headings
        self.video_tree.heading("id", text="ID")
        self.video_tree.heading("title", text="Title")
        self.video_tree.heading("status", text="Status")
        self.video_tree.heading("created_at", text="Created")

        # Define column widths
        self.video_tree.column("id", width=50)
        self.video_tree.column("title", width=200)
        self.video_tree.column("status", width=80)
        self.video_tree.column("created_at", width=120)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.video_tree.yview)
        self.video_tree.configure(yscrollcommand=scrollbar.set)

        # Pack widgets
        self.video_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind selection event
        self.video_tree.bind("<<TreeviewSelect>>", self.on_video_select)

    def create_generation_controls(self):
        """Create video generation controls"""
        control_frame = ttk.LabelFrame(self.right_frame, text="Generate New Video")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Workflow mode selector
        workflow_frame = ttk.Frame(control_frame)
        workflow_frame.grid(row=0, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)

        ttk.Label(workflow_frame, text="Workflow:").pack(side=tk.LEFT, padx=5)
        self.workflow_mode_var = tk.StringVar(value="Enhanced AI" if self.use_enhanced_workflow else "Legacy")
        workflow_combo = ttk.Combobox(workflow_frame, textvariable=self.workflow_mode_var,
                                     values=["Enhanced AI", "Legacy"], state="readonly", width=15)
        workflow_combo.pack(side=tk.LEFT, padx=5)
        workflow_combo.bind("<<ComboboxSelected>>", self.on_workflow_changed)

        # Workflow info button
        ttk.Button(workflow_frame, text="?", width=3,
                  command=self.show_workflow_info).pack(side=tk.LEFT, padx=5)

        # Additional context for story generation
        ttk.Label(control_frame, text="Theme (optional):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.theme_var = tk.StringVar()
        ttk.Entry(control_frame, textvariable=self.theme_var, width=30).grid(row=1, column=1, padx=5, pady=5)

        # Generate button
        generate_button = ttk.Button(control_frame, text="Generate Video", command=self.generate_video)
        generate_button.grid(row=2, column=0, columnspan=2, padx=5, pady=10)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=3, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)

        # Status label
        self.generation_status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(control_frame, textvariable=self.generation_status_var)
        status_label.grid(row=4, column=0, columnspan=2, padx=5, pady=5)

    def create_preview_area(self):
        """Create preview area for videos and stories"""
        preview_frame = ttk.LabelFrame(self.right_frame, text="Preview")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create notebook for preview tabs
        preview_notebook = ttk.Notebook(preview_frame)
        preview_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Story tab
        story_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(story_frame, text="Story")

        self.story_text = scrolledtext.ScrolledText(story_frame, wrap=tk.WORD, height=10)
        self.story_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Image tab
        image_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(image_frame, text="Image")

        self.image_label = ttk.Label(image_frame)
        self.image_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Video tab
        video_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(video_frame, text="Video")

        video_info_frame = ttk.Frame(video_frame)
        video_info_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(video_info_frame, text="Video Path:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.video_path_var = tk.StringVar()
        ttk.Entry(video_info_frame, textvariable=self.video_path_var, width=40, state="readonly").grid(row=0, column=1, padx=5, pady=5)

        ttk.Button(video_info_frame, text="Play", command=self.play_video).grid(row=0, column=2, padx=5, pady=5)

        # Video thumbnail
        self.video_thumbnail = ttk.Label(video_frame)
        self.video_thumbnail.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_action_buttons(self):
        """Create action buttons"""
        button_frame = ttk.Frame(self.right_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        # Export button
        self.export_button = ttk.Button(button_frame, text="Export Video", command=self.export_video, state=tk.DISABLED)
        self.export_button.pack(side=tk.LEFT, padx=5)

        # Delete button
        self.delete_button = ttk.Button(button_frame, text="Delete", command=self.delete_video, state=tk.DISABLED)
        self.delete_button.pack(side=tk.LEFT, padx=5)

        # Refresh button
        ttk.Button(button_frame, text="Refresh", command=self.refresh_videos).pack(side=tk.RIGHT, padx=5)

    def refresh_accounts(self):
        """Refresh account list from database"""
        try:
            # Get active accounts from database
            accounts = self.db_manager.get_all_accounts(active_only=True)

            # Update account dropdown
            account_values = [f"{account['username']} (ID: {account['id']})" for account in accounts]
            self.account_combo['values'] = account_values

            # Store account data
            self.accounts = accounts

            if accounts:
                self.account_combo.current(0)
                self.on_account_selected(None)
        except Exception as e:
            self.set_status(f"Error loading accounts: {str(e)}")
            messagebox.showerror("Error", f"Failed to load accounts: {str(e)}")

    def on_account_selected(self, event):
        """Handle account selection

        Args:
            event: Selection event
        """
        if not self.account_var.get():
            return

        # Extract account ID from selection
        account_id = int(self.account_var.get().split("ID: ")[1].rstrip(")"))

        # Find account data
        account = next((a for a in self.accounts if a['id'] == account_id), None)
        if not account:
            return

        # Update account info labels
        self.niche_label.config(text=account['niche'])
        self.duration_label.config(text=f"{account['video_duration']}s")
        self.voice_label.config(text=account['voice_type'])
        self.status_label.config(text=account['status'])

        # Store selected account
        self.selected_account = account

        # Refresh videos for this account
        self.refresh_videos()

    def refresh_videos(self):
        """Refresh video list from database"""
        # Clear existing items
        for item in self.video_tree.get_children():
            self.video_tree.delete(item)

        if not hasattr(self, 'selected_account'):
            return

        try:
            # Get videos for selected account
            videos = self.db_manager.get_account_videos(self.selected_account['id'])

            # Add videos to treeview
            for video in videos:
                # Format created_at timestamp
                created_at = video['created_at'].split('.')[0].replace('T', ' ') if 'T' in video['created_at'] else video['created_at']

                self.video_tree.insert("", tk.END, values=(
                    video['id'],
                    video['title'],
                    video['status'],
                    created_at
                ))

            self.set_status(f"Loaded {len(videos)} videos for account {self.selected_account['username']}")
        except Exception as e:
            self.set_status(f"Error loading videos: {str(e)}")

    def on_video_select(self, event):
        """Handle video selection in treeview

        Args:
            event: Selection event
        """
        # Get selected item
        selected_items = self.video_tree.selection()
        if not selected_items:
            return

        # Get video data
        item = selected_items[0]
        video_id = int(self.video_tree.item(item, "values")[0])

        try:
            # Get video details from database
            video = self.db_manager.get_video(video_id)
            if not video:
                return

            # Update preview
            self.story_text.delete(1.0, tk.END)
            self.story_text.insert(tk.END, video['story_text'])

            # Update image preview if available
            if video['image_path'] and os.path.exists(video['image_path']):
                self.display_image(video['image_path'])
            else:
                self.clear_image_preview()

            # Update video preview if available
            if video['video_path'] and os.path.exists(video['video_path']):
                self.video_path_var.set(video['video_path'])
                self.display_video_thumbnail(video['video_path'])
            else:
                self.video_path_var.set("")
                self.clear_video_preview()

            # Update button states
            self.export_button.config(state=tk.NORMAL if video['status'] == 'generated' else tk.DISABLED)
            self.delete_button.config(state=tk.NORMAL)

            # Store selected video
            self.selected_video = video
        except Exception as e:
            self.set_status(f"Error loading video details: {str(e)}")

    def display_image(self, image_path):
        """Display image in preview

        Args:
            image_path: Path to image file
        """
        try:
            # Open and resize image
            img = Image.open(image_path)
            img = img.resize((300, 400), Image.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(img)

            # Update label
            self.image_label.config(image=photo)
            self.image_label.image = photo  # Keep reference
        except Exception as e:
            self.set_status(f"Error displaying image: {str(e)}")
            self.clear_image_preview()

    def clear_image_preview(self):
        """Clear image preview"""
        self.image_label.config(image=None)
        self.image_label.image = None

    def display_video_thumbnail(self, video_path):
        """Display video thumbnail

        Args:
            video_path: Path to video file
        """
        try:
            # Use first frame of video as thumbnail
            import cv2
            cap = cv2.VideoCapture(video_path)
            ret, frame = cap.read()
            cap.release()

            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # Convert to PIL Image and resize
                img = Image.fromarray(frame)
                img = img.resize((300, 400), Image.LANCZOS)

                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(img)

                # Update label
                self.video_thumbnail.config(image=photo)
                self.video_thumbnail.image = photo  # Keep reference
            else:
                self.clear_video_preview()
        except Exception as e:
            self.set_status(f"Error displaying video thumbnail: {str(e)}")
            self.clear_video_preview()

    def clear_video_preview(self):
        """Clear video preview"""
        self.video_thumbnail.config(image=None)
        self.video_thumbnail.image = None

    def play_video(self):
        """Play selected video"""
        if not self.video_path_var.get() or not os.path.exists(self.video_path_var.get()):
            messagebox.showerror("Error", "No valid video file selected")
            return

        try:
            # Open video with default player
            video_path = os.path.normpath(self.video_path_var.get())
            os.startfile(video_path)
        except Exception as e:
            self.set_status(f"Error playing video: {str(e)}")
            messagebox.showerror("Error", f"Failed to play video: {str(e)}")

    def generate_video(self):
        """Generate a new video for the selected account"""
        if not hasattr(self, 'selected_account'):
            messagebox.showerror("Error", "No account selected")
            return

        # Check which workflow to use
        if self.use_enhanced_workflow and self.enhanced_pipeline:
            # Use enhanced AI workflow
            threading.Thread(target=self._generate_video_enhanced_thread, daemon=True).start()
        else:
            # Use legacy workflow
            # Initialize components if needed
            if not self.story_generator:
                self.story_generator = StoryGenerator(self.config)

            if not self.speech_generator:
                self.speech_generator = SpeechGenerator(self.config)

            if not self.image_generator:
                self.image_generator = ImageGenerator(self.config)

            if not self.video_generator:
                self.video_generator = VideoGenerator(self.config)

            # Start generation in a separate thread
            threading.Thread(target=self._generate_video_thread, daemon=True).start()

    def _generate_video_thread(self):
        """Video generation thread"""
        try:
            account = self.selected_account
            self.set_status(f"Generating video for account: {account['username']}")

            # Update UI
            self.generation_status_var.set("Generating story...")
            self.progress_var.set(10)

            # 1. Generate story
            additional_context = {}
            if self.theme_var.get().strip():
                additional_context['theme'] = self.theme_var.get().strip()

            story_result = self.story_generator.generate_story(
                account['niche'],
                account['video_duration'],
                additional_context
            )

            # Update UI
            self.story_text.delete(1.0, tk.END)
            self.story_text.insert(tk.END, story_result['text'])
            self.generation_status_var.set("Generating speech...")
            self.progress_var.set(30)

            # 2. Generate speech
            audio_path = self.speech_generator.generate_speech(
                story_result['text'],
                account['voice_type']
            )

            # Update UI
            self.generation_status_var.set("Generating image...")
            self.progress_var.set(50)

            # 3. Generate image
            image_path = self.image_generator.generate_image(
                story_result['title'],
                account['niche']
            )

            # Update image preview
            self.display_image(image_path)

            # Update UI
            self.generation_status_var.set("Generating video...")
            self.progress_var.set(70)

            # 4. Generate video
            video_result = self.video_generator.generate_video(
                story_result['text'],
                audio_path,
                image_path
            )

            # Update video preview
            self.video_path_var.set(video_result['path'])
            self.display_video_thumbnail(video_result['path'])

            # Update UI
            self.generation_status_var.set("Saving to database...")
            self.progress_var.set(90)

            # 5. Save to database
            video_data = {
                'account_id': account['id'],
                'title': story_result['title'],
                'story_text': story_result['text'],
                'audio_path': audio_path,
                'image_path': image_path,
                'video_path': video_result['path'],
                'status': 'generated'
            }

            video_id = self.db_manager.add_video(video_data)

            # Update UI
            self.generation_status_var.set("Video generated successfully!")
            self.progress_var.set(100)
            self.set_status(f"Generated video: {story_result['title']} (ID: {video_id})")

            # Refresh video list
            self.refresh_videos()

            # Show success message
            messagebox.showinfo("Success", "Video generated successfully!")
        except Exception as e:
            error_msg = f"Error generating video: {str(e)}"
            self.set_status(error_msg)
            self.generation_status_var.set(f"Error: {str(e)}")
            messagebox.showerror("Error", error_msg)
        finally:
            # Reset progress after a delay
            self.after(3000, lambda: self.progress_var.set(0))

    def _generate_video_enhanced_thread(self):
        """Enhanced video generation thread using AI workflow"""
        try:
            account = self.selected_account
            self.set_status(f"Generating video with Enhanced AI workflow for: {account['username']}")

            # Update UI
            self.generation_status_var.set("Starting Enhanced AI workflow...")
            self.progress_var.set(5)

            # Run the enhanced pipeline asynchronously
            import asyncio

            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Run the enhanced generation pipeline
                result = loop.run_until_complete(
                    self.enhanced_pipeline.generate_video(account)
                )

                if result.get('success'):
                    # Update UI with results
                    story = result.get('story', {})
                    self.story_text.delete(1.0, tk.END)
                    self.story_text.insert(tk.END, story.get('text', ''))

                    # Display generated image
                    if result.get('image_path') and os.path.exists(result['image_path']):
                        self.display_image(result['image_path'])

                    # Display generated video
                    if result.get('video_path') and os.path.exists(result['video_path']):
                        self.video_path_var.set(result['video_path'])
                        self.display_video_thumbnail(result['video_path'])

                    # Update progress and status
                    self.progress_var.set(100)
                    self.generation_status_var.set("Enhanced video generated successfully!")

                    viral_score = story.get('viral_score', 0)
                    self.set_status(f"Enhanced video generated: {story.get('title', 'Untitled')} (Viral Score: {viral_score:.1f})")

                    # Record generation attempt
                    if self.enhanced_account_manager:
                        self.enhanced_account_manager.record_generation_attempt(
                            account_id=account['id'],
                            success=True,
                            viral_score=viral_score
                        )

                    # Refresh video list
                    self.refresh_videos()

                    # Show success message
                    messagebox.showinfo("Success",
                        f"Enhanced video generated successfully!\n"
                        f"Title: {story.get('title', 'Untitled')}\n"
                        f"Viral Score: {viral_score:.1f}/100")
                else:
                    # Handle generation failure
                    error_msg = result.get('error', 'Unknown error')
                    self.generation_status_var.set(f"Enhanced generation failed: {error_msg}")
                    self.set_status(f"Enhanced generation failed: {error_msg}")

                    # Record failed attempt
                    if self.enhanced_account_manager:
                        self.enhanced_account_manager.record_generation_attempt(
                            account_id=account['id'],
                            success=False,
                            error=error_msg
                        )

                    messagebox.showerror("Error", f"Enhanced video generation failed:\n{error_msg}")

            finally:
                loop.close()

        except Exception as e:
            error_msg = f"Error in enhanced video generation: {str(e)}"
            self.set_status(error_msg)
            self.generation_status_var.set(f"Enhanced generation error: {str(e)}")

            # Record failed attempt
            if hasattr(self, 'enhanced_account_manager') and self.enhanced_account_manager:
                self.enhanced_account_manager.record_generation_attempt(
                    account_id=self.selected_account['id'],
                    success=False,
                    error=str(e)
                )

            messagebox.showerror("Error", error_msg)
        finally:
            # Reset progress after a delay
            self.after(3000, lambda: self.progress_var.set(0))

    def export_video(self):
        """Export selected video for upload"""
        if not hasattr(self, 'selected_video'):
            messagebox.showerror("Error", "No video selected")
            return

        # Initialize export manager if needed
        if not self.export_manager:
            self.export_manager = ExportManager(self.config)

        try:
            video = self.selected_video
            account = self.selected_account

            # Export video
            export_result = self.export_manager.export_video(
                video['video_path'],
                account,
                video
            )

            # Mark as exported in database
            self.db_manager.mark_video_uploaded(video['id'])

            # Show success message
            self.set_status(f"Exported video to: {export_result['export']['path']}")
            messagebox.showinfo("Success", f"Video exported successfully to:\n{export_result['export']['path']}")

            # Refresh video list
            self.refresh_videos()
        except Exception as e:
            error_msg = f"Error exporting video: {str(e)}"
            self.set_status(error_msg)
            messagebox.showerror("Error", error_msg)

    def delete_video(self):
        """Delete selected video"""
        if not hasattr(self, 'selected_video'):
            messagebox.showerror("Error", "No video selected")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete",
                                  f"Are you sure you want to delete video '{self.selected_video['title']}'?"):
            return

        try:
            video = self.selected_video

            # Delete associated files
            for path_key in ['audio_path', 'image_path', 'video_path']:
                if video[path_key] and os.path.exists(video[path_key]):
                    try:
                        os.remove(video[path_key])
                    except Exception as e:
                        logging.warning(f"Could not delete file {video[path_key]}: {str(e)}")

            # Delete from database
            # Note: We need to add this method to the DatabaseManager
            # For now, we'll just show a message
            self.set_status(f"Deleted video: {video['title']} (ID: {video['id']})")
            messagebox.showinfo("Success", "Video deleted successfully")

            # Clear preview
            self.story_text.delete(1.0, tk.END)
            self.clear_image_preview()
            self.clear_video_preview()
            self.video_path_var.set("")

            # Reset button states
            self.export_button.config(state=tk.DISABLED)
            self.delete_button.config(state=tk.DISABLED)

            # Refresh video list
            self.refresh_videos()
        except Exception as e:
            error_msg = f"Error deleting video: {str(e)}"
            self.set_status(error_msg)
            messagebox.showerror("Error", error_msg)

    def _initialize_enhanced_components(self):
        """Initialize enhanced AI workflow components"""
        try:
            # Initialize enhanced account manager
            self.enhanced_account_manager = EnhancedAccountManager(self.db_manager, self.config)

            # Initialize enhanced pipeline
            self.enhanced_pipeline = VideoGenerationPipeline(
                config=self.config,
                db_manager=self.db_manager,
                status_callback=self.set_status
            )

            # Initialize auto scheduler (but don't start it automatically)
            self.auto_scheduler = AutoGenerationScheduler(
                config=self.config,
                db_manager=self.db_manager,
                pipeline=self.enhanced_pipeline,
                account_manager=self.enhanced_account_manager
            )

            # Set scheduler callbacks
            self.auto_scheduler.set_callbacks(
                status_callback=self.set_status,
                generation_callback=self._on_scheduled_generation
            )

            logging.info("Enhanced AI workflow components initialized")

        except Exception as e:
            logging.error(f"Error initializing enhanced components: {str(e)}")
            self.use_enhanced_workflow = False  # Fallback to legacy workflow

    def _on_scheduled_generation(self, event_type: str, account: dict, result: dict):
        """Handle scheduled generation events"""
        if event_type == 'success':
            self.set_status(f"Auto-generated video for {account['username']}: {result.get('story', {}).get('title', 'Untitled')}")
            # Refresh video list to show new video
            self.refresh_videos()
        elif event_type == 'failure':
            self.set_status(f"Auto-generation failed for {account['username']}: {result.get('error', 'Unknown error')}")

    def toggle_workflow_mode(self):
        """Toggle between enhanced and legacy workflow"""
        self.use_enhanced_workflow = not self.use_enhanced_workflow
        mode = "Enhanced AI" if self.use_enhanced_workflow else "Legacy"
        self.set_status(f"Switched to {mode} workflow")
        messagebox.showinfo("Workflow Mode", f"Switched to {mode} workflow")

    def start_auto_scheduler(self):
        """Start the auto-generation scheduler"""
        if not self.auto_scheduler:
            messagebox.showerror("Error", "Auto-scheduler not available")
            return

        try:
            self.auto_scheduler.start_scheduler()
            messagebox.showinfo("Scheduler", "Auto-generation scheduler started")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start scheduler: {str(e)}")

    def stop_auto_scheduler(self):
        """Stop the auto-generation scheduler"""
        if not self.auto_scheduler:
            messagebox.showerror("Error", "Auto-scheduler not available")
            return

        try:
            self.auto_scheduler.stop_scheduler()
            messagebox.showinfo("Scheduler", "Auto-generation scheduler stopped")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop scheduler: {str(e)}")

    def on_workflow_changed(self, event):
        """Handle workflow mode change"""
        new_mode = self.workflow_mode_var.get()
        self.use_enhanced_workflow = (new_mode == "Enhanced AI")

        if self.use_enhanced_workflow and not self.enhanced_pipeline:
            messagebox.showwarning("Warning",
                "Enhanced AI workflow not available. Please check API keys in settings.\n"
                "Falling back to Legacy workflow.")
            self.use_enhanced_workflow = False
            self.workflow_mode_var.set("Legacy")

        mode_name = "Enhanced AI" if self.use_enhanced_workflow else "Legacy"
        self.set_status(f"Switched to {mode_name} workflow")

    def show_workflow_info(self):
        """Show information about workflow modes"""
        info_text = """Workflow Modes:

🚀 Enhanced AI Workflow:
• Uses advanced AI tools for viral content
• Midjourney image analysis & generation
• AppyHub high-quality audio
• Hedra animated video assembly
• GPT prompt optimization
• Viral score calculation
• Requires API keys for all services

🔧 Legacy Workflow:
• Uses basic local/free tools
• Simple image generation
• gTTS for speech
• MoviePy for video assembly
• Works without external API keys
• Faster but lower quality output

Choose Enhanced AI for best results or Legacy for quick testing."""

        messagebox.showinfo("Workflow Information", info_text)

    def add_scheduler_controls(self):
        """Add scheduler control buttons to action buttons frame"""
        # Find the action buttons frame
        button_frame = None
        for child in self.right_frame.winfo_children():
            if isinstance(child, ttk.Frame):
                # Check if this frame contains buttons
                for subchild in child.winfo_children():
                    if isinstance(subchild, ttk.Button):
                        button_frame = child
                        break
                if button_frame:
                    break

        if button_frame:
            # Add separator
            ttk.Separator(button_frame, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=10)

            # Add scheduler buttons
            ttk.Button(button_frame, text="Start Auto",
                      command=self.start_auto_scheduler).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="Stop Auto",
                      command=self.stop_auto_scheduler).pack(side=tk.LEFT, padx=5)

    def get_generation_stats(self):
        """Get and display generation statistics"""
        if not self.enhanced_pipeline:
            messagebox.showinfo("Statistics", "Enhanced pipeline not available")
            return

        stats = self.enhanced_pipeline.get_generation_stats()

        stats_text = f"""Generation Statistics:

Total Generations: {stats['total']}
Successful: {stats['successful']}
Failed: {stats['failed']}
Success Rate: {stats['success_rate']:.1f}%
Average Viral Score: {stats['avg_viral_score']:.1f}/100

Scheduler Status: {'Running' if self.auto_scheduler and self.auto_scheduler.is_running else 'Stopped'}
"""

        if self.auto_scheduler:
            scheduler_stats = self.auto_scheduler.get_scheduler_status()
            stats_text += f"""
Scheduled Generations: {scheduler_stats['scheduled_generations']}
Auto Success Rate: {scheduler_stats['success_rate']:.1f}%
"""

        messagebox.showinfo("Generation Statistics", stats_text)
