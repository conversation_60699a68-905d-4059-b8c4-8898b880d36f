"""
Configuration for Simplified TikTok Automation
Only using: OpenAI, ApyHub, PiAPI, Hedra
"""

import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class APIConfig:
    """API configuration for all services"""
    
    # OpenAI Configuration
    openai_api_key: str = ""
    openai_model: str = "gpt-4"
    
    # ApyHub Configuration (Text-to-Audio)
    apyhub_api_key: str = ""
    apyhub_endpoint: str = "https://api.apyhub.com/convert/text-to-audio"
    
    # PiAPI Configuration (Midjourney)
    piapi_api_key: str = ""
    piapi_endpoint: str = "https://api.piapi.ai/api/v1/task"
    
    # Hedra Configuration (Video Generation)
    hedra_api_key: str = ""
    hedra_endpoint: str = "https://api.hedra.com"
    
    @classmethod
    def from_env(cls) -> 'APIConfig':
        """Load configuration from environment variables"""
        return cls(
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            apyhub_api_key=os.getenv('APYHUB_API_KEY', ''),
            piapi_api_key=os.getenv('PIAPI_API_KEY', ''),
            hedra_api_key=os.getenv('HEDRA_API_KEY', '')
        )
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'APIConfig':
        """Load configuration from dictionary"""
        return cls(
            openai_api_key=config_dict.get('openai_api_key', ''),
            apyhub_api_key=config_dict.get('apyhub_api_key', ''),
            piapi_api_key=config_dict.get('piapi_api_key', ''),
            hedra_api_key=config_dict.get('hedra_api_key', '')
        )
    
    def validate(self) -> bool:
        """Validate that all required API keys are present"""
        required_keys = [
            self.openai_api_key,
            self.apyhub_api_key,
            self.piapi_api_key,
            self.hedra_api_key
        ]
        return all(key.strip() for key in required_keys)

# Video Configuration
@dataclass
class VideoConfig:
    """Configuration for video generation"""
    
    # TikTok Format
    width: int = 1080
    height: int = 1920  # 9:16 aspect ratio
    max_duration: int = 60  # seconds
    
    # Content Settings
    script_length: int = 60  # words for 60-second video
    niche: str = "scary_stories"  # Default niche
    
    # Output Settings
    output_dir: str = "output"
    temp_dir: str = "temp"
    
    # Quality Settings
    audio_quality: str = "high"
    image_quality: str = "high"
    video_quality: str = "high"

# Prompt Templates
class PromptTemplates:
    """Centralized prompt templates for consistency"""
    
    STORY_GENERATION = """
    Create a compelling {niche} story for TikTok that will captivate viewers in 60 seconds.
    
    Requirements:
    - Exactly {word_count} words
    - Hook viewers in first 3 seconds
    - Include emotional triggers
    - Build suspense throughout
    - End with impact
    - Use conversational language perfect for narration
    - Include vivid visual descriptions for scene creation
    
    Format as JSON:
    {{
        "title": "Catchy title with emojis",
        "script": "The complete story text",
        "hook": "Opening hook sentence",
        "visual_scene": "Detailed description of main visual scene for image generation",
        "mood": "Overall mood/atmosphere",
        "duration_estimate": "Estimated seconds"
    }}
    """
    
    VISUAL_ENHANCEMENT = """
    You are an expert prompt engineer specializing in crafting hyperrealistic visual generation prompts from reference descriptions. 
    
    Transform this scene description into a perfect Midjourney prompt:
    
    Scene: {scene_description}
    Mood: {mood}
    
    Requirements:
    - Hyperrealistic, photographic quality
    - Optimized for TikTok vertical format (9:16)
    - Include specific lighting, composition, and atmosphere
    - Remove any text, UI elements, or branding
    - Focus on emotional impact and visual storytelling
    - Keep under 500 characters
    
    Return only the enhanced prompt, no explanations.
    """
    
    BABY_TRANSFORMATION = """
    You are an advanced visual transformation AI specialized in generating photorealistic baby versions of described subjects while preserving all key visual, spatial, and contextual elements.
    
    Scene Description: {scene_description}
    
    Transform the primary subject into a photorealistic baby version while maintaining:
    - Exact pose, expression, and environment
    - All clothing and accessories (resized appropriately)
    - Same lighting, camera angle, and composition
    - Photorealistic rendering (not cartoon)
    - Natural baby proportions for 16-month-old
    
    Return a single enhanced prompt under 700 characters with no line breaks.
    """

# Default Configuration
DEFAULT_CONFIG = {
    "api": APIConfig(),
    "video": VideoConfig(),
    "prompts": PromptTemplates()
}
