"""
Simplified TikTok Automation Pipeline
Using only: OpenAI, ApyHub, PiAPI, Hedra
"""

import asyncio
import logging
import os
import time
from typing import Dict, Any, Optional
from pathlib import Path

from .config import APIConfig, VideoConfig
from .services.openai_service import OpenAIService
from .services.apyhub_service import ApyHubService
from .services.piapi_service import PiAPIService
from .services.hedra_service import HedraService

class TikTokPipeline:
    """Simplified TikTok video generation pipeline"""
    
    def __init__(self, api_config: APIConfig, video_config: VideoConfig):
        self.api_config = api_config
        self.video_config = video_config
        
        # Validate configuration
        if not api_config.validate():
            raise ValueError("Invalid API configuration - missing required keys")
        
        # Create output directories
        self._setup_directories()
        
        # Initialize services
        self.openai = OpenAIService(api_config)
        self.apyhub = ApyHubService(api_config)
        self.piapi = PiAPIService(api_config)
        self.hedra = HedraService(api_config)
        
        logging.info("TikTok Pipeline initialized successfully")
    
    def _setup_directories(self):
        """Create necessary output directories"""
        directories = [
            self.video_config.output_dir,
            self.video_config.temp_dir,
            f"{self.video_config.temp_dir}/audio",
            f"{self.video_config.temp_dir}/images",
            f"{self.video_config.temp_dir}/videos"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    async def generate_video(self, 
                           niche: str = None, 
                           custom_prompt: Optional[str] = None,
                           voice: str = "en-US-AriaNeural",
                           transformation_type: str = "normal") -> Dict[str, Any]:
        """Generate a complete TikTok video
        
        Args:
            niche: Content niche (scary_stories, mystery, etc.)
            custom_prompt: Custom story prompt (overrides niche)
            voice: Voice for TTS
            transformation_type: 'normal', 'baby', or 'enhanced'
            
        Returns:
            Dict containing all generated assets and metadata
        """
        niche = niche or self.video_config.niche
        generation_id = int(time.time())
        
        logging.info(f"Starting TikTok video generation (ID: {generation_id})")
        
        try:
            # Step 1: Generate Story Script
            logging.info("Step 1/6: Generating story script...")
            if custom_prompt:
                story_data = {
                    "title": "Custom Story",
                    "script": custom_prompt,
                    "visual_scene": "A dramatic scene matching the story",
                    "mood": "engaging",
                    "duration_estimate": "60"
                }
            else:
                story_data = await self.openai.generate_story(
                    niche=niche, 
                    word_count=self.video_config.script_length
                )
            
            # Step 2: Convert Script to Audio
            logging.info("Step 2/6: Converting script to audio...")
            audio_path = f"{self.video_config.temp_dir}/audio/audio_{generation_id}.mp3"
            
            # Optimize script for duration
            optimized_script = await self.apyhub.optimize_text_for_duration(
                story_data['script'], 
                self.video_config.max_duration
            )
            
            await self.apyhub.text_to_audio(
                text=optimized_script,
                output_path=audio_path,
                voice=voice
            )
            
            # Step 3: Enhance Visual Prompt
            logging.info("Step 3/6: Enhancing visual prompt...")
            if transformation_type == "baby":
                enhanced_prompt = await self.openai.create_baby_transformation_prompt(
                    story_data['visual_scene']
                )
            else:
                enhanced_prompt = await self.openai.enhance_visual_prompt(
                    story_data['visual_scene'],
                    story_data.get('mood', 'dramatic')
                )
            
            # Step 4: Generate Image
            logging.info("Step 4/6: Generating image with Midjourney...")
            image_result = await self.piapi.generate_image(
                prompt=enhanced_prompt,
                aspect_ratio="9:16"
            )
            
            # Download generated image
            image_path = f"{self.video_config.temp_dir}/images/image_{generation_id}.jpg"
            await self.piapi.download_image(image_result['image_url'], image_path)
            
            # Step 5: Generate Video
            logging.info("Step 5/6: Creating video with Hedra...")
            video_path = f"{self.video_config.temp_dir}/videos/video_{generation_id}.mp4"
            
            video_result = await self.hedra.create_video(
                image_path=image_path,
                audio_path=audio_path,
                output_path=video_path
            )
            
            # Step 6: Move to Final Output
            logging.info("Step 6/6: Finalizing output...")
            final_video_path = f"{self.video_config.output_dir}/tiktok_video_{generation_id}.mp4"
            
            # Copy video to final location
            import shutil
            shutil.copy2(video_path, final_video_path)
            
            # Compile results
            result = {
                'generation_id': generation_id,
                'story_data': story_data,
                'enhanced_prompt': enhanced_prompt,
                'audio_path': audio_path,
                'image_path': image_path,
                'image_url': image_result['image_url'],
                'video_path': final_video_path,
                'niche': niche,
                'voice': voice,
                'transformation_type': transformation_type,
                'timestamp': time.time(),
                'duration_estimate': await self.apyhub.estimate_audio_duration(optimized_script, voice),
                'metadata': {
                    'piapi_task_id': image_result['task_id'],
                    'hedra_generation_id': video_result['generation_id'],
                    'script_length': len(optimized_script.split()),
                    'original_script_length': len(story_data['script'].split())
                }
            }
            
            logging.info(f"Video generation completed successfully: {final_video_path}")
            return result
            
        except Exception as e:
            logging.error(f"Error in video generation: {str(e)}")
            raise
    
    async def generate_batch(self, 
                           count: int, 
                           niche: str = None,
                           voice: str = "en-US-AriaNeural",
                           delay_between: int = 30) -> list:
        """Generate multiple videos in batch
        
        Args:
            count: Number of videos to generate
            niche: Content niche
            voice: Voice for TTS
            delay_between: Delay between generations (seconds)
            
        Returns:
            List of generation results
        """
        logging.info(f"Starting batch generation: {count} videos")
        
        results = []
        
        for i in range(count):
            try:
                logging.info(f"Generating video {i+1}/{count}")
                
                result = await self.generate_video(
                    niche=niche,
                    voice=voice
                )
                
                results.append(result)
                
                # Delay between generations to avoid rate limits
                if i < count - 1:  # Don't delay after last video
                    logging.info(f"Waiting {delay_between}s before next generation...")
                    await asyncio.sleep(delay_between)
                    
            except Exception as e:
                logging.error(f"Error generating video {i+1}: {str(e)}")
                results.append({
                    'error': str(e),
                    'index': i+1,
                    'timestamp': time.time()
                })
        
        logging.info(f"Batch generation completed: {len([r for r in results if 'error' not in r])}/{count} successful")
        return results
    
    async def describe_and_recreate(self, image_url: str, voice: str = "en-US-AriaNeural") -> Dict[str, Any]:
        """Describe an existing image and recreate it as a TikTok video
        
        Args:
            image_url: URL of image to describe and recreate
            voice: Voice for TTS
            
        Returns:
            Generation result
        """
        logging.info(f"Describing and recreating image: {image_url}")
        
        try:
            # Describe the image
            description_result = await self.piapi.describe_image(image_url)
            primary_description = description_result['primary_description']
            
            # Create a story based on the description
            story_prompt = f"Create a short, engaging story inspired by this scene: {primary_description}"
            
            # Generate video using the description as visual prompt
            result = await self.generate_video(
                custom_prompt=story_prompt,
                voice=voice
            )
            
            # Add description metadata
            result['original_image_url'] = image_url
            result['image_description'] = primary_description
            result['description_task_id'] = description_result['task_id']
            
            return result
            
        except Exception as e:
            logging.error(f"Error in describe and recreate: {str(e)}")
            raise
    
    async def close(self):
        """Close all service connections"""
        await asyncio.gather(
            self.openai.close(),
            self.apyhub.close(),
            self.piapi.close(),
            self.hedra.close(),
            return_exceptions=True
        )
        logging.info("Pipeline closed")
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
