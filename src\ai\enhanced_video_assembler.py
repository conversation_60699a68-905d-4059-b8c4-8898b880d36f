"""
Enhanced Video Assembler for TikTok Automation
Multiple video generation options with fallbacks
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
import json

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. Enhanced video assembler will not be available.")

from src.ai.video_assembler import HedraVideoAssembler

class EnhancedVideoAssembler:
    """Enhanced video assembler with multiple providers and fallbacks"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize enhanced video assembler

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.hedra_key = config['api_keys'].get('hedra', '')
        self.did_key = config['api_keys'].get('did', '')

        # Initialize providers
        self.hedra_assembler = None
        self.did_assembler = None

        if self.hedra_key:
            self.hedra_assembler = HedraVideoAssembler(config)

        if self.did_key:
            self.did_assembler = DIDVideoAssembler(config)

        # Fallback to enhanced MoviePy
        self.moviepy_assembler = EnhancedMoviePyAssembler(config)

        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "videos")
        os.makedirs(self.output_dir, exist_ok=True)

    async def generate_video(self, image_path: str, audio_path: str,
                           output_filename: Optional[str] = None,
                           animation_style: str = "natural",
                           story_text: str = "") -> Dict[str, Any]:
        """Generate video using best available provider

        Args:
            image_path: Path to the source image
            audio_path: Path to the audio file
            output_filename: Optional filename for output
            animation_style: Animation style preference
            story_text: Story text for subtitles

        Returns:
            Dict containing video generation result
        """
        logging.info(f"Starting enhanced video generation: {image_path} + {audio_path}")

        # Try providers in order of preference
        providers = []

        if self.hedra_assembler:
            providers.append(("Hedra", self.hedra_assembler))

        if self.did_assembler:
            providers.append(("D-ID", self.did_assembler))

        # Always have MoviePy as fallback
        providers.append(("Enhanced MoviePy", self.moviepy_assembler))

        last_error = None

        for provider_name, assembler in providers:
            try:
                logging.info(f"Trying video generation with {provider_name}")

                if provider_name == "Enhanced MoviePy":
                    # Use sync method for MoviePy with story text
                    result = assembler.generate_video(image_path, audio_path, output_filename, animation_style, story_text)
                else:
                    # Use async method for API providers
                    result = await assembler.generate_video(image_path, audio_path, output_filename, animation_style)

                # Add provider info to result
                result['provider'] = provider_name
                result['fallback_used'] = provider_name != "Hedra"

                logging.info(f"Video generated successfully with {provider_name}")
                return result

            except Exception as e:
                last_error = e
                logging.warning(f"{provider_name} failed: {str(e)}")
                continue

        # If all providers failed
        raise Exception(f"All video generation providers failed. Last error: {str(last_error)}")

    def get_available_providers(self) -> List[str]:
        """Get list of available video generation providers

        Returns:
            List: Available provider names
        """
        providers = []

        if self.hedra_assembler:
            providers.append("Hedra (Premium)")

        if self.did_assembler:
            providers.append("D-ID (Alternative)")

        providers.append("Enhanced MoviePy (Free)")

        return providers

    async def close(self):
        """Close all assemblers"""
        try:
            if self.hedra_assembler:
                await self.hedra_assembler.close()
            if self.did_assembler:
                await self.did_assembler.close()
        except Exception as e:
            logging.warning(f"Error closing assemblers: {str(e)}")


class DIDVideoAssembler:
    """D-ID API integration for video assembly"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize D-ID video assembler"""
        self.config = config
        self.api_key = config['api_keys']['did']

        # API endpoints
        self.base_url = "https://api.d-id.com"
        self.talks_endpoint = f"{self.base_url}/talks"

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=300.0,
            headers={
                "Authorization": f"Basic {self.api_key}",
                "Content-Type": "application/json"
            }
        )

        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "videos")
        os.makedirs(self.output_dir, exist_ok=True)

    async def generate_video(self, image_path: str, audio_path: str,
                           output_filename: Optional[str] = None,
                           animation_style: str = "natural") -> Dict[str, Any]:
        """Generate video using D-ID API"""
        if not self.api_key:
            raise ValueError("D-ID API key not configured")

        logging.info(f"Generating video with D-ID: {image_path} + {audio_path}")

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"did_video_{timestamp}.mp4"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Upload image and audio, then create talk
            talk_id = await self._create_talk(image_path, audio_path)

            # Wait for completion
            video_url = await self._wait_for_completion(talk_id)

            # Download video
            await self._download_video(video_url, output_path)

            result = {
                'path': output_path,
                'talk_id': talk_id,
                'animation_style': animation_style,
                'provider': 'D-ID',
                'timestamp': time.time()
            }

            logging.info(f"D-ID video generated successfully: {output_path}")
            return result

        except Exception as e:
            logging.error(f"Error generating video with D-ID: {str(e)}")
            raise

    async def _create_talk(self, image_path: str, audio_path: str) -> str:
        """Create a talk using D-ID API"""
        # This is a simplified implementation
        # In reality, you'd need to upload files first
        payload = {
            "source_url": image_path,  # Would need to be a public URL
            "script": {
                "type": "audio",
                "audio_url": audio_path  # Would need to be a public URL
            },
            "config": {
                "fluent": True,
                "pad_audio": 0.0
            }
        }

        response = await self.client.post(self.talks_endpoint, json=payload)
        response.raise_for_status()

        result = response.json()
        return result['id']

    async def _wait_for_completion(self, talk_id: str) -> str:
        """Wait for D-ID talk completion"""
        max_wait = 300  # 5 minutes
        start_time = time.time()

        while time.time() - start_time < max_wait:
            response = await self.client.get(f"{self.talks_endpoint}/{talk_id}")
            response.raise_for_status()

            result = response.json()
            status = result['status']

            if status == 'done':
                return result['result_url']
            elif status == 'error':
                raise Exception(f"D-ID generation failed: {result.get('error', 'Unknown error')}")

            await asyncio.sleep(5)

        raise TimeoutError("D-ID video generation timed out")

    async def _download_video(self, video_url: str, output_path: str):
        """Download generated video"""
        response = await self.client.get(video_url)
        response.raise_for_status()

        with open(output_path, 'wb') as f:
            f.write(response.content)

    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()


class EnhancedMoviePyAssembler:
    """Enhanced MoviePy video assembler with better effects"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize enhanced MoviePy assembler"""
        self.config = config
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "videos")
        os.makedirs(self.output_dir, exist_ok=True)

    def generate_video(self, image_path: str, audio_path: str,
                      output_filename: Optional[str] = None,
                      animation_style: str = "natural",
                      story_text: str = "") -> Dict[str, Any]:
        """Generate video using enhanced MoviePy with anime-style effects"""
        try:
            from moviepy.editor import (ImageClip, AudioFileClip, CompositeVideoClip,
                                      TextClip, concatenate_videoclips, ColorClip)
            import numpy as np
        except ImportError as e:
            logging.error(f"MoviePy import error: {str(e)}")
            raise ImportError("MoviePy not properly installed. Please reinstall with: pip install moviepy")

        logging.info(f"Generating enhanced anime-style video: {image_path} + {audio_path}")

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"enhanced_anime_video_{timestamp}.mp4"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Load audio to get duration
            audio = AudioFileClip(audio_path)
            duration = audio.duration

            # Create base image clip
            image_clip = ImageClip(image_path, duration=duration)

            # Resize to TikTok format (9:16) - 1080x1920
            target_width, target_height = 1080, 1920

            # Calculate scaling to fill the frame
            img_w, img_h = image_clip.size
            scale_w = target_width / img_w
            scale_h = target_height / img_h
            scale = max(scale_w, scale_h)  # Scale to fill

            # Resize and center crop
            image_clip = image_clip.resize(scale)
            image_clip = image_clip.crop(width=target_width, height=target_height,
                                       x_center=image_clip.w/2, y_center=image_clip.h/2)

            # Apply anime-style animation effects
            if animation_style == "dramatic":
                # Slow zoom with parallax effect
                def zoom_effect(t):
                    zoom_factor = 1 + 0.15 * (t / duration)  # Gradual zoom
                    return zoom_factor
                image_clip = image_clip.resize(zoom_effect)

            elif animation_style == "subtle":
                # Ken Burns effect (pan and zoom)
                def ken_burns(t):
                    progress = t / duration
                    zoom = 1 + 0.1 * progress
                    x_offset = 50 * np.sin(progress * np.pi)
                    return zoom

                def position_effect(t):
                    progress = t / duration
                    x_offset = int(30 * np.sin(progress * 2 * np.pi))
                    return ('center', 'center')

                image_clip = image_clip.resize(ken_burns).set_position(position_effect)

            else:  # natural - breathing effect
                def breathing_effect(t):
                    return 1 + 0.05 * np.sin(t * 2 * np.pi / 3)  # Slow breathing
                image_clip = image_clip.resize(breathing_effect)

            # Add subtle color grading for anime feel
            image_clip = image_clip.fx(lambda clip: clip.fx('colorx', 1.1))  # Slight saturation boost

            # Create animated subtitles if story text provided
            clips = [image_clip]

            if story_text:
                # Split text into chunks for better readability
                words = story_text.split()
                chunk_size = 8  # Words per subtitle
                chunks = [' '.join(words[i:i+chunk_size]) for i in range(0, len(words), chunk_size)]

                chunk_duration = duration / len(chunks)

                for i, chunk in enumerate(chunks):
                    start_time = i * chunk_duration
                    end_time = min((i + 1) * chunk_duration, duration)

                    try:
                        # Create modern subtitle style with fixed parameters
                        txt_clip = TextClip(
                            chunk,
                            fontsize=45,
                            color='white',
                            font='Arial-Bold',
                            stroke_color='black',
                            stroke_width=2,
                            method='caption',
                            size=(int(target_width - 100), None),  # Ensure integer
                            align='center'
                        ).set_position(('center', int(target_height - 200))).set_start(float(start_time)).set_end(float(end_time))

                        # Add fade in/out effect
                        txt_clip = txt_clip.crossfadein(0.3).crossfadeout(0.3)
                        clips.append(txt_clip)

                    except Exception as e:
                        logging.warning(f"Failed to create subtitle for chunk {i}: {str(e)}")
                        # Fallback to simple text
                        try:
                            txt_clip = TextClip(
                                chunk,
                                fontsize=40,
                                color='white',
                                font='Arial'
                            ).set_position(('center', int(target_height - 150))).set_start(float(start_time)).set_end(float(end_time))
                            clips.append(txt_clip)
                        except Exception as e2:
                            logging.warning(f"Fallback subtitle also failed for chunk {i}: {str(e2)}")
                            continue  # Skip this subtitle if it fails

            # Combine all clips
            if len(clips) > 1:
                final_video = CompositeVideoClip(clips, size=(target_width, target_height))
            else:
                final_video = image_clip

            # Set audio
            final_video = final_video.set_audio(audio)

            # Write video file with optimized settings
            final_video.write_videofile(
                output_path,
                fps=30,  # Higher FPS for smoother animation
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None,
                preset='medium',  # Good quality/speed balance
                ffmpeg_params=['-crf', '23']  # Good quality
            )

            # Clean up
            audio.close()
            if len(clips) > 1:
                for clip in clips:
                    if hasattr(clip, 'close'):
                        clip.close()
            else:
                image_clip.close()
            final_video.close()

            result = {
                'path': output_path,
                'animation_style': animation_style,
                'provider': 'Enhanced MoviePy (Anime Style)',
                'duration': duration,
                'has_subtitles': bool(story_text),
                'timestamp': time.time()
            }

            logging.info(f"Enhanced anime-style video generated successfully: {output_path}")
            return result

        except Exception as e:
            logging.error(f"Error generating enhanced video: {str(e)}")
            raise
