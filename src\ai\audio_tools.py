"""
ElevenLabs Audio Tools for TikTok Automation
Text-to-Speech and audio generation via ElevenLabs API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
import json

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. ElevenLabs audio tools will not be available.")

class ElevenLabsAudio:
    """ElevenLabs audio generation service"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize ElevenLabs audio service

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['elevenlabs']

        # API endpoints
        self.base_url = "https://api.elevenlabs.io/v1"
        self.tts_endpoint = f"{self.base_url}/text-to-speech"
        self.voices_endpoint = f"{self.base_url}/voices"
        self.models_endpoint = f"{self.base_url}/models"

        # ElevenLabs voice configurations (using popular voice IDs)
        self.voice_mapping = {
            'male': 'pNInz6obpgDQGcFmaJgB',      # Adam (male, deep)
            'female': 'EXAVITQu4vr4xnSDxMaL',    # <PERSON> (female, young)
            'ai': 'VR6AewLTigWG4xSOukaG',        # Arnold (male, crisp)
            'male1': 'pNInz6obpgDQGcFmaJgB',     # Adam
            'male2': 'VR6AewLTigWG4xSOukaG',     # Arnold
            'female1': 'EXAVITQu4vr4xnSDxMaL',  # Bella
            'female2': 'AZnzlk1XvdvUeBnXmlld',  # Domi (female, strong)
            'ai1': 'VR6AewLTigWG4xSOukaG',      # Arnold
            'ai2': 'pNInz6obpgDQGcFmaJgB'       # Adam
        }

        # Voice names for display
        self.voice_names = {
            'male': 'Adam (Deep Male)',
            'female': 'Bella (Young Female)',
            'ai': 'Arnold (Crisp AI)',
            'male1': 'Adam (Deep Male)',
            'male2': 'Arnold (Crisp Male)',
            'female1': 'Bella (Young Female)',
            'female2': 'Domi (Strong Female)',
            'ai1': 'Arnold (Crisp AI)',
            'ai2': 'Adam (Deep AI)'
        }

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=60.0,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )

        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "audio")
        os.makedirs(self.output_dir, exist_ok=True)

    async def generate_speech(self, text: str, voice_type: str,
                            output_filename: Optional[str] = None) -> str:
        """Generate speech from text using ElevenLabs TTS

        Args:
            text: Text to convert to speech
            voice_type: Voice type (male, female, ai, etc.)
            output_filename: Optional filename for output

        Returns:
            str: Path to generated audio file
        """
        if not self.api_key:
            raise ValueError("ElevenLabs API key not configured")

        logging.info(f"Generating speech with ElevenLabs: {len(text)} chars, voice: {voice_type}")

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"speech_{timestamp}.mp3"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Map voice type to ElevenLabs voice ID
            voice_id = self.voice_mapping.get(voice_type, self.voice_mapping['ai'])

            # Prepare request payload for ElevenLabs
            payload = {
                "text": text,
                "model_id": "eleven_monolingual_v1",  # Fast, good quality model
                "voice_settings": {
                    "stability": 0.5,      # Voice consistency
                    "similarity_boost": 0.75,  # Voice similarity
                    "style": 0.0,          # Style exaggeration
                    "use_speaker_boost": True
                }
            }

            # ElevenLabs TTS endpoint with voice ID
            tts_url = f"{self.base_url}/text-to-speech/{voice_id}"

            # Make API request
            response = await self.client.post(
                tts_url,
                json=payload,
                headers={
                    "Accept": "audio/mpeg",
                    "Content-Type": "application/json",
                    "xi-api-key": self.api_key
                }
            )
            response.raise_for_status()

            # ElevenLabs returns audio directly, not JSON
            if response.headers.get("content-type", "").startswith("audio/"):
                # Save audio directly
                with open(output_path, 'wb') as f:
                    f.write(response.content)

                logging.info(f"Speech generated successfully: {output_path}")
                return output_path
            else:
                # Handle error response
                try:
                    error_data = response.json()
                    error_msg = error_data.get('detail', {}).get('message', 'Unknown error')
                except:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                raise Exception(f"ElevenLabs TTS failed: {error_msg}")

        except Exception as e:
            logging.error(f"Error generating speech with ElevenLabs: {str(e)}")
            raise

    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get available voices from ElevenLabs

        Returns:
            List: Available voices with metadata
        """
        if not self.api_key:
            raise ValueError("ElevenLabs API key not configured")

        try:
            response = await self.client.get(
                self.voices_endpoint,
                headers={"xi-api-key": self.api_key}
            )
            response.raise_for_status()

            result = response.json()
            voices = result.get('voices', [])

            # Format voice information
            formatted_voices = []
            for voice in voices:
                formatted_voices.append({
                    'voice_id': voice['voice_id'],
                    'name': voice['name'],
                    'category': voice.get('category', 'Unknown'),
                    'description': voice.get('description', ''),
                    'preview_url': voice.get('preview_url', ''),
                    'labels': voice.get('labels', {})
                })

            logging.info(f"Retrieved {len(formatted_voices)} voices from ElevenLabs")
            return formatted_voices

        except Exception as e:
            logging.error(f"Error getting voices: {str(e)}")
            raise

    async def clone_voice(self, name: str, audio_files: List[str],
                         description: str = "") -> Dict[str, Any]:
        """Clone a voice using ElevenLabs voice cloning

        Args:
            name: Name for the cloned voice
            audio_files: List of audio file paths for training
            description: Description of the voice

        Returns:
            Dict: Cloned voice information
        """
        if not self.api_key:
            raise ValueError("ElevenLabs API key not configured")

        logging.info(f"Cloning voice: {name} with {len(audio_files)} audio files")

        try:
            # Prepare files for upload
            files = []
            for i, audio_file in enumerate(audio_files):
                if not os.path.exists(audio_file):
                    raise FileNotFoundError(f"Audio file not found: {audio_file}")

                with open(audio_file, 'rb') as f:
                    files.append(('files', (f"sample_{i}.mp3", f.read(), 'audio/mpeg')))

            # Add voice data
            data = {
                'name': name,
                'description': description
            }

            response = await self.client.post(
                f"{self.base_url}/voices/add",
                files=files,
                data=data,
                headers={"xi-api-key": self.api_key}
            )
            response.raise_for_status()

            result = response.json()

            logging.info(f"Voice cloned successfully: {result.get('voice_id')}")
            return result

        except Exception as e:
            logging.error(f"Error cloning voice: {str(e)}")
            raise

    async def get_character_count(self, text: str) -> Dict[str, Any]:
        """Get character count and cost estimation for text

        Args:
            text: Text to analyze

        Returns:
            Dict: Character count and cost information
        """
        char_count = len(text)

        # ElevenLabs pricing (approximate)
        # Free tier: 10,000 characters/month
        # Starter: $5/month for 30,000 characters
        # Creator: $22/month for 100,000 characters

        cost_per_char = 0.00022  # Approximate cost for Creator plan
        estimated_cost = char_count * cost_per_char

        return {
            'character_count': char_count,
            'estimated_cost_usd': round(estimated_cost, 4),
            'free_tier_remaining': max(0, 10000 - char_count) if char_count <= 10000 else 0,
            'exceeds_free_tier': char_count > 10000
        }

    async def enhance_audio(self, audio_path: str,
                          enhancement_type: str = "normalize") -> str:
        """Enhance audio quality using basic processing

        Args:
            audio_path: Path to audio file
            enhancement_type: Type of enhancement (normalize, denoise, etc.)

        Returns:
            str: Path to enhanced audio file
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        logging.info(f"Enhancing audio: {audio_path} (type: {enhancement_type})")

        # Generate output filename
        base_name = os.path.splitext(os.path.basename(audio_path))[0]
        output_filename = f"{base_name}_enhanced.mp3"
        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Basic audio enhancement (copy for now)
            # In production, could use pydub or other audio processing libraries
            import shutil
            shutil.copy2(audio_path, output_path)

            logging.info(f"Audio enhanced: {output_path}")
            return output_path

        except Exception as e:
            logging.error(f"Error enhancing audio: {str(e)}")
            raise

    def get_voice_options(self) -> Dict[str, List[str]]:
        """Get available voice options

        Returns:
            Dict: Voice options organized by category
        """
        return {
            'male': ['male', 'male1', 'male2'],
            'female': ['female', 'female1', 'female2'],
            'ai': ['ai', 'ai1', 'ai2']
        }

    def get_voice_names(self) -> Dict[str, str]:
        """Get voice names for display

        Returns:
            Dict: Voice type to display name mapping
        """
        return self.voice_names

    def estimate_speech_duration(self, text: str, voice_type: str = 'ai') -> float:
        """Estimate speech duration for given text

        Args:
            text: Text to analyze
            voice_type: Voice type for speed estimation

        Returns:
            float: Estimated duration in seconds
        """
        # ElevenLabs speaking rates (words per minute) - generally faster than traditional TTS
        speaking_rates = {
            'male': 160,      # Adam - natural pace
            'female': 170,    # Bella - slightly faster
            'ai': 155,        # Arnold - crisp and clear
            'male1': 160,     # Adam
            'male2': 155,     # Arnold
            'female1': 170,   # Bella
            'female2': 165,   # Domi - strong but measured
            'ai1': 155,       # Arnold
            'ai2': 160        # Adam
        }

        rate = speaking_rates.get(voice_type, 160)
        word_count = len(text.split())
        duration = (word_count / rate) * 60

        return duration

    def get_model_options(self) -> List[str]:
        """Get available ElevenLabs models

        Returns:
            List: Available model IDs
        """
        return [
            "eleven_monolingual_v1",    # Fast, good quality
            "eleven_multilingual_v1",   # Supports multiple languages
            "eleven_multilingual_v2",   # Latest multilingual model
            "eleven_turbo_v2"           # Fastest generation
        ]

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
