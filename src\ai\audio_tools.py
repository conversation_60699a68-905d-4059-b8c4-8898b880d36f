"""
AppyHub Audio Tools for TikTok Automation
Text-to-Speech and audio generation via AppyHub API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional
import json

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. AppyHub audio tools will not be available.")

class AppyHubAudio:
    """AppyHub audio generation service"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize AppyHub audio service
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['appyhub']
        
        # API endpoints
        self.base_url = "https://api.appyhub.com/v1"
        self.tts_endpoint = f"{self.base_url}/text-to-speech"
        self.video_to_audio_endpoint = f"{self.base_url}/video-to-audio"
        
        # Voice configurations
        self.voice_mapping = {
            'male': 'en-US-AriaNeural',
            'female': 'en-US-JennyNeural', 
            'ai': 'en-US-GuyNeural',
            'male1': 'en-US-ChristopherNeural',
            'male2': 'en-US-EricNeural',
            'female1': 'en-US-AriaNeural',
            'female2': 'en-US-SaraNeural',
            'ai1': 'en-US-GuyNeural',
            'ai2': 'en-US-DavisNeural'
        }
        
        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=60.0,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )
        
        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "audio")
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def generate_speech(self, text: str, voice_type: str, 
                            output_filename: Optional[str] = None) -> str:
        """Generate speech from text using AppyHub TTS
        
        Args:
            text: Text to convert to speech
            voice_type: Voice type (male, female, ai, etc.)
            output_filename: Optional filename for output
            
        Returns:
            str: Path to generated audio file
        """
        if not self.api_key:
            raise ValueError("AppyHub API key not configured")
        
        logging.info(f"Generating speech with AppyHub: {len(text)} chars, voice: {voice_type}")
        
        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"speech_{timestamp}.mp3"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        try:
            # Map voice type to actual voice
            voice_name = self.voice_mapping.get(voice_type, self.voice_mapping['ai'])
            
            # Prepare request payload
            payload = {
                "text": text,
                "voice": voice_name,
                "speed": 1.0,
                "pitch": 0,
                "volume": 1.0,
                "format": "mp3",
                "quality": "high"
            }
            
            # Make API request
            response = await self.client.post(self.tts_endpoint, json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            if not result.get('success'):
                raise Exception(f"AppyHub TTS failed: {result.get('error', 'Unknown error')}")
            
            # Download the generated audio
            audio_url = result['data']['audio_url']
            await self._download_audio(audio_url, output_path)
            
            logging.info(f"Speech generated successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Error generating speech with AppyHub: {str(e)}")
            raise
    
    async def extract_audio_from_video(self, video_path: str, 
                                     output_filename: Optional[str] = None) -> str:
        """Extract audio from video using AppyHub
        
        Args:
            video_path: Path to video file
            output_filename: Optional filename for output
            
        Returns:
            str: Path to extracted audio file
        """
        if not self.api_key:
            raise ValueError("AppyHub API key not configured")
        
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        logging.info(f"Extracting audio from video: {video_path}")
        
        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"extracted_audio_{timestamp}.mp3"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        try:
            # Upload video file
            with open(video_path, 'rb') as video_file:
                files = {'video': video_file}
                data = {
                    'format': 'mp3',
                    'quality': 'high'
                }
                
                response = await self.client.post(
                    self.video_to_audio_endpoint, 
                    files=files, 
                    data=data
                )
                response.raise_for_status()
            
            result = response.json()
            
            if not result.get('success'):
                raise Exception(f"AppyHub video-to-audio failed: {result.get('error', 'Unknown error')}")
            
            # Download the extracted audio
            audio_url = result['data']['audio_url']
            await self._download_audio(audio_url, output_path)
            
            logging.info(f"Audio extracted successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Error extracting audio: {str(e)}")
            raise
    
    async def _download_audio(self, audio_url: str, output_path: str) -> None:
        """Download audio file from URL
        
        Args:
            audio_url: URL of the audio file
            output_path: Local path to save the audio
        """
        try:
            response = await self.client.get(audio_url)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"Audio downloaded: {output_path}")
            
        except Exception as e:
            logging.error(f"Error downloading audio: {str(e)}")
            raise
    
    async def enhance_audio(self, audio_path: str, 
                          enhancement_type: str = "normalize") -> str:
        """Enhance audio quality
        
        Args:
            audio_path: Path to audio file
            enhancement_type: Type of enhancement (normalize, denoise, etc.)
            
        Returns:
            str: Path to enhanced audio file
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        logging.info(f"Enhancing audio: {audio_path}")
        
        # Generate output filename
        base_name = os.path.splitext(os.path.basename(audio_path))[0]
        output_filename = f"{base_name}_enhanced.mp3"
        output_path = os.path.join(self.output_dir, output_filename)
        
        try:
            # For now, implement basic audio enhancement
            # In production, this could use AppyHub's audio enhancement APIs
            
            # Simple copy for now - replace with actual enhancement logic
            import shutil
            shutil.copy2(audio_path, output_path)
            
            logging.info(f"Audio enhanced: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Error enhancing audio: {str(e)}")
            raise
    
    def get_voice_options(self) -> Dict[str, List[str]]:
        """Get available voice options
        
        Returns:
            Dict: Voice options organized by category
        """
        return {
            'male': ['male', 'male1', 'male2'],
            'female': ['female', 'female1', 'female2'],
            'ai': ['ai', 'ai1', 'ai2']
        }
    
    def estimate_speech_duration(self, text: str, voice_type: str = 'ai') -> float:
        """Estimate speech duration for given text
        
        Args:
            text: Text to analyze
            voice_type: Voice type for speed estimation
            
        Returns:
            float: Estimated duration in seconds
        """
        # Average speaking rates (words per minute)
        speaking_rates = {
            'male': 150,
            'female': 160,
            'ai': 140,
            'male1': 145,
            'male2': 155,
            'female1': 165,
            'female2': 155,
            'ai1': 140,
            'ai2': 135
        }
        
        rate = speaking_rates.get(voice_type, 150)
        word_count = len(text.split())
        duration = (word_count / rate) * 60
        
        return duration
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
