"""
PiAPI Service for Midjourney Image Generation and Description
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
import httpx
from ..config import APIConfig

class PiAPIService:
    """Service for PiAPI Midjourney integration"""

    def __init__(self, config: APIConfig):
        self.config = config
        self.client = httpx.AsyncClient(
            timeout=300.0,  # 5 minutes for image generation
            headers={
                "Authorization": f"Bearer {config.piapi_api_key}",
                "Content-Type": "application/json"
            }
        )

    async def describe_image(self, image_url: str) -> Dict[str, Any]:
        """Describe an image using Midjourney's describe feature

        Args:
            image_url: Public URL of the image to describe

        Returns:
            Dict containing image description data
        """
        logging.info(f"Describing image: {image_url}")

        payload = {
            "model": "midjourney",
            "task_type": "describe",
            "input": {
                "image_url": image_url
            }
        }

        try:
            # Submit describe task
            response = await self.client.post(self.config.piapi_endpoint, json=payload)
            response.raise_for_status()

            task_data = response.json()
            task_id = task_data.get('task_id')

            if not task_id:
                raise Exception("No task_id returned from PiAPI")

            # Wait for completion
            result = await self._wait_for_completion(task_id)

            # Extract descriptions
            descriptions = result.get('output', {}).get('descriptions', [])

            return {
                'task_id': task_id,
                'descriptions': descriptions,
                'primary_description': descriptions[0] if descriptions else "",
                'timestamp': time.time()
            }

        except Exception as e:
            logging.error(f"Error describing image: {str(e)}")
            raise

    async def generate_image(self, prompt: str, aspect_ratio: str = "9:16") -> Dict[str, Any]:
        """Generate image using Midjourney via PiAPI

        Args:
            prompt: Text prompt for image generation
            aspect_ratio: Aspect ratio (9:16 for TikTok)

        Returns:
            Dict containing generated image data
        """
        logging.info(f"Generating image: {prompt[:100]}...")

        # Enhance prompt for TikTok format with Midjourney parameters
        enhanced_prompt = f"{prompt} --ar {aspect_ratio} --v 6"

        payload = {
            "model": "midjourney",
            "task_type": "imagine",
            "input": {
                "prompt": enhanced_prompt
            }
        }

        try:
            # Submit imagine task
            response = await self.client.post(self.config.piapi_endpoint, json=payload)
            response.raise_for_status()

            task_data = response.json()
            task_id = task_data.get('task_id')

            if not task_id:
                raise Exception("No task_id returned from PiAPI")

            # Wait for completion
            result = await self._wait_for_completion(task_id)

            # Extract image URLs
            output = result.get('output', {})
            image_url = output.get('image_url', '')

            return {
                'task_id': task_id,
                'image_url': image_url,
                'prompt': enhanced_prompt,
                'aspect_ratio': aspect_ratio,
                'timestamp': time.time(),
                'output': output
            }

        except Exception as e:
            logging.error(f"Error generating image: {str(e)}")
            raise

    async def upscale_image(self, task_id: str, index: int = 1) -> Dict[str, Any]:
        """Upscale a generated image

        Args:
            task_id: Original generation task ID
            index: Image index to upscale (1-4)

        Returns:
            Dict containing upscaled image data
        """
        logging.info(f"Upscaling image from task {task_id}, index {index}")

        payload = {
            "model": "midjourney",
            "task_type": "upscale",
            "input": {
                "origin_task_id": task_id,
                "index": str(index)
            }
        }

        try:
            response = await self.client.post(self.config.piapi_endpoint, json=payload)
            response.raise_for_status()

            task_data = response.json()
            upscale_task_id = task_data.get('task_id')

            # Wait for upscale completion
            result = await self._wait_for_completion(upscale_task_id)

            output = result.get('output', {})

            return {
                'task_id': upscale_task_id,
                'original_task_id': task_id,
                'image_url': output.get('image_url', ''),
                'index': index,
                'timestamp': time.time(),
                'output': output
            }

        except Exception as e:
            logging.error(f"Error upscaling image: {str(e)}")
            raise

    async def _wait_for_completion(self, task_id: str, max_wait: int = 300) -> Dict[str, Any]:
        """Wait for task completion

        Args:
            task_id: Task ID to monitor
            max_wait: Maximum wait time in seconds

        Returns:
            Completed task result
        """
        start_time = time.time()

        while time.time() - start_time < max_wait:
            try:
                # Check task status
                response = await self.client.get(f"{self.config.piapi_endpoint}/{task_id}")
                response.raise_for_status()

                result = response.json()
                status = result.get('status')

                if status == 'completed':
                    logging.info(f"Task {task_id} completed successfully")
                    return result
                elif status == 'failed':
                    error_msg = result.get('error', 'Unknown error')
                    raise Exception(f"Task failed: {error_msg}")
                elif status in ['pending', 'processing']:
                    logging.debug(f"Task {task_id} status: {status}")
                    await asyncio.sleep(10)  # Wait 10 seconds before next check
                else:
                    logging.warning(f"Unknown task status: {status}")
                    await asyncio.sleep(5)

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 404:
                    logging.warning(f"Task {task_id} not found, waiting...")
                    await asyncio.sleep(5)
                else:
                    raise

        raise TimeoutError(f"Task {task_id} did not complete within {max_wait} seconds")

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get current status of a task

        Args:
            task_id: Task ID to check

        Returns:
            Task status information
        """
        try:
            response = await self.client.get(f"{self.config.piapi_endpoint}/{task_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logging.error(f"Error getting task status: {str(e)}")
            raise

    async def download_image(self, image_url: str, output_path: str) -> str:
        """Download image from URL to local file

        Args:
            image_url: URL of the image to download
            output_path: Local path to save the image

        Returns:
            Path to downloaded image
        """
        logging.info(f"Downloading image: {image_url}")

        try:
            response = await self.client.get(image_url)
            response.raise_for_status()

            # Ensure output directory exists
            import os
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Save image
            with open(output_path, 'wb') as f:
                f.write(response.content)

            logging.info(f"Image downloaded: {output_path}")
            return output_path

        except Exception as e:
            logging.error(f"Error downloading image: {str(e)}")
            raise

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
