"""
ApyHub Service for Text-to-Audio Conversion
"""

import asyncio
import logging
import os
from typing import Optional
import httpx
from ..config import APIConfig

class ApyHubService:
    """Service for ApyHub Text-to-Audio conversion"""
    
    def __init__(self, config: APIConfig):
        self.config = config
        self.client = httpx.AsyncClient(
            timeout=120.0,  # Longer timeout for audio generation
            headers={
                "apy-token": config.apyhub_api_key,
                "Content-Type": "application/json"
            }
        )
        
    async def text_to_audio(self, text: str, output_path: str, voice: str = "en-US-AriaNeural") -> str:
        """Convert text to audio using ApyHub
        
        Args:
            text: Text content to convert
            output_path: Path to save the audio file
            voice: Voice to use for TTS
            
        Returns:
            Path to generated audio file
        """
        logging.info(f"Converting text to audio: {len(text)} characters")
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        payload = {
            "text": text,
            "voice": voice,
            "format": "mp3",
            "speed": 1.0,
            "pitch": 1.0,
            "volume": 1.0
        }
        
        try:
            response = await self.client.post(
                self.config.apyhub_endpoint,
                json=payload
            )
            response.raise_for_status()
            
            # ApyHub returns audio data directly
            audio_data = response.content
            
            # Save audio file
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            logging.info(f"Audio generated successfully: {output_path}")
            return output_path
            
        except httpx.HTTPStatusError as e:
            logging.error(f"ApyHub API error {e.response.status_code}: {e.response.text}")
            raise Exception(f"ApyHub TTS failed: {e.response.status_code}")
        except Exception as e:
            logging.error(f"Error generating audio: {str(e)}")
            raise
    
    async def get_available_voices(self) -> list:
        """Get list of available voices from ApyHub
        
        Returns:
            List of available voice options
        """
        # Common ApyHub voices for TikTok content
        return [
            "en-US-AriaNeural",      # Female, clear
            "en-US-DavisNeural",     # Male, professional
            "en-US-JennyNeural",     # Female, friendly
            "en-US-GuyNeural",       # Male, casual
            "en-US-AmberNeural",     # Female, warm
            "en-US-AnaNeural",       # Female, young
            "en-US-BrandonNeural",   # Male, energetic
            "en-US-ChristopherNeural", # Male, deep
            "en-US-CoraNeural",      # Female, professional
            "en-US-ElizabethNeural"  # Female, mature
        ]
    
    async def estimate_audio_duration(self, text: str, voice: str = "en-US-AriaNeural") -> float:
        """Estimate audio duration for given text
        
        Args:
            text: Text to analyze
            voice: Voice type for estimation
            
        Returns:
            Estimated duration in seconds
        """
        # Average speaking rates (words per minute) for different voices
        speaking_rates = {
            "en-US-AriaNeural": 160,
            "en-US-DavisNeural": 150,
            "en-US-JennyNeural": 165,
            "en-US-GuyNeural": 155,
            "en-US-AmberNeural": 160,
            "en-US-AnaNeural": 170,
            "en-US-BrandonNeural": 175,
            "en-US-ChristopherNeural": 145,
            "en-US-CoraNeural": 155,
            "en-US-ElizabethNeural": 150
        }
        
        rate = speaking_rates.get(voice, 160)  # Default to 160 WPM
        word_count = len(text.split())
        duration = (word_count / rate) * 60
        
        return duration
    
    async def validate_text_length(self, text: str, max_duration: int = 60) -> bool:
        """Validate if text will fit within duration limit
        
        Args:
            text: Text to validate
            max_duration: Maximum allowed duration in seconds
            
        Returns:
            True if text fits within duration limit
        """
        estimated_duration = await self.estimate_audio_duration(text)
        return estimated_duration <= max_duration
    
    async def optimize_text_for_duration(self, text: str, target_duration: int = 60) -> str:
        """Optimize text length to fit target duration
        
        Args:
            text: Original text
            target_duration: Target duration in seconds
            
        Returns:
            Optimized text that fits duration
        """
        current_duration = await self.estimate_audio_duration(text)
        
        if current_duration <= target_duration:
            return text
        
        # Calculate target word count
        words = text.split()
        target_words = int(len(words) * (target_duration / current_duration))
        
        # Truncate to target length while preserving sentence structure
        optimized_words = words[:target_words]
        
        # Try to end on a complete sentence
        optimized_text = ' '.join(optimized_words)
        if '.' in optimized_text:
            sentences = optimized_text.split('.')
            if len(sentences) > 1:
                optimized_text = '.'.join(sentences[:-1]) + '.'
        
        logging.info(f"Optimized text from {len(words)} to {len(optimized_text.split())} words")
        return optimized_text
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
