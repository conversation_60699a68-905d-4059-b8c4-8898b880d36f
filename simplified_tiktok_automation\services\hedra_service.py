"""
Hedra Service for Video Generation
"""

import asyncio
import logging
import time
import base64
from typing import Dict, Any, Optional
import httpx
from ..config import APIConfig

class HedraService:
    """Service for Hedra video generation"""
    
    def __init__(self, config: APIConfig):
        self.config = config
        self.client = httpx.AsyncClient(
            timeout=600.0,  # 10 minutes for video generation
            headers={
                "Authorization": f"Bearer {config.hedra_api_key}",
                "Content-Type": "application/json"
            }
        )
        self.base_url = config.hedra_endpoint
        
    async def create_video(self, image_path: str, audio_path: str, output_path: str) -> Dict[str, Any]:
        """Create video from image and audio
        
        Args:
            image_path: Path to source image
            audio_path: Path to audio file
            output_path: Path to save generated video
            
        Returns:
            Dict containing video generation result
        """
        logging.info(f"Creating video: {image_path} + {audio_path}")
        
        try:
            # Step 1: Upload image and audio assets
            image_asset = await self._upload_asset(image_path, "image")
            audio_asset = await self._upload_asset(audio_path, "audio")
            
            # Step 2: Generate video
            video_result = await self._generate_video(image_asset['id'], audio_asset['id'])
            
            # Step 3: Download generated video
            video_url = video_result['video_url']
            await self._download_video(video_url, output_path)
            
            return {
                'video_path': output_path,
                'video_url': video_url,
                'image_asset_id': image_asset['id'],
                'audio_asset_id': audio_asset['id'],
                'generation_id': video_result['id'],
                'timestamp': time.time()
            }
            
        except Exception as e:
            logging.error(f"Error creating video: {str(e)}")
            raise
    
    async def _upload_asset(self, file_path: str, asset_type: str) -> Dict[str, Any]:
        """Upload asset (image or audio) to Hedra
        
        Args:
            file_path: Path to file to upload
            asset_type: 'image' or 'audio'
            
        Returns:
            Asset information
        """
        logging.info(f"Uploading {asset_type}: {file_path}")
        
        # Read file and encode as base64
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        base64_data = base64.b64encode(file_data).decode('utf-8')
        
        # Determine MIME type
        mime_types = {
            'image': 'image/jpeg' if file_path.lower().endswith(('.jpg', '.jpeg')) else 'image/png',
            'audio': 'audio/mpeg' if file_path.lower().endswith('.mp3') else 'audio/wav'
        }
        
        payload = {
            "type": asset_type,
            "data": f"data:{mime_types[asset_type]};base64,{base64_data}"
        }
        
        try:
            response = await self.client.post(f"{self.base_url}/assets", json=payload)
            response.raise_for_status()
            
            asset_data = response.json()
            logging.info(f"{asset_type.title()} uploaded successfully: {asset_data.get('id')}")
            
            return asset_data
            
        except Exception as e:
            logging.error(f"Error uploading {asset_type}: {str(e)}")
            raise
    
    async def _generate_video(self, image_asset_id: str, audio_asset_id: str) -> Dict[str, Any]:
        """Generate video from uploaded assets
        
        Args:
            image_asset_id: ID of uploaded image asset
            audio_asset_id: ID of uploaded audio asset
            
        Returns:
            Video generation result
        """
        logging.info(f"Generating video: image={image_asset_id}, audio={audio_asset_id}")
        
        payload = {
            "image_asset_id": image_asset_id,
            "audio_asset_id": audio_asset_id,
            "aspect_ratio": "9:16",  # TikTok format
            "quality": "high"
        }
        
        try:
            # Submit video generation request
            response = await self.client.post(f"{self.base_url}/generate", json=payload)
            response.raise_for_status()
            
            generation_data = response.json()
            generation_id = generation_data.get('id')
            
            if not generation_id:
                raise Exception("No generation ID returned from Hedra")
            
            # Wait for video generation to complete
            result = await self._wait_for_video_completion(generation_id)
            
            return result
            
        except Exception as e:
            logging.error(f"Error generating video: {str(e)}")
            raise
    
    async def _wait_for_video_completion(self, generation_id: str, max_wait: int = 600) -> Dict[str, Any]:
        """Wait for video generation to complete
        
        Args:
            generation_id: Video generation ID
            max_wait: Maximum wait time in seconds
            
        Returns:
            Completed video generation result
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                response = await self.client.get(f"{self.base_url}/generate/{generation_id}")
                response.raise_for_status()
                
                result = response.json()
                status = result.get('status')
                
                if status == 'completed':
                    video_url = result.get('video_url')
                    if video_url:
                        logging.info(f"Video generation completed: {generation_id}")
                        return {
                            'id': generation_id,
                            'video_url': video_url,
                            'status': status,
                            'result': result
                        }
                    else:
                        raise Exception("Video completed but no URL provided")
                        
                elif status == 'failed':
                    error_msg = result.get('error', 'Unknown error')
                    raise Exception(f"Video generation failed: {error_msg}")
                    
                elif status in ['pending', 'processing']:
                    progress = result.get('progress', 0)
                    logging.info(f"Video generation progress: {progress}%")
                    await asyncio.sleep(15)  # Check every 15 seconds
                    
                else:
                    logging.warning(f"Unknown video generation status: {status}")
                    await asyncio.sleep(10)
                    
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 404:
                    logging.warning(f"Generation {generation_id} not found, waiting...")
                    await asyncio.sleep(10)
                else:
                    raise
        
        raise TimeoutError(f"Video generation {generation_id} did not complete within {max_wait} seconds")
    
    async def _download_video(self, video_url: str, output_path: str) -> str:
        """Download generated video
        
        Args:
            video_url: URL of generated video
            output_path: Local path to save video
            
        Returns:
            Path to downloaded video
        """
        logging.info(f"Downloading video: {video_url}")
        
        try:
            response = await self.client.get(video_url)
            response.raise_for_status()
            
            # Ensure output directory exists
            import os
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save video
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"Video downloaded: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Error downloading video: {str(e)}")
            raise
    
    async def get_generation_status(self, generation_id: str) -> Dict[str, Any]:
        """Get status of video generation
        
        Args:
            generation_id: Generation ID to check
            
        Returns:
            Generation status information
        """
        try:
            response = await self.client.get(f"{self.base_url}/generate/{generation_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logging.error(f"Error getting generation status: {str(e)}")
            raise
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
